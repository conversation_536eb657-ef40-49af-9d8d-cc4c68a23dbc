#!/usr/bin/env python3
"""
生產環境啟動腳本
使用Waitress WSGI服務器運行Flask應用
"""

import os
import sys
from dotenv import load_dotenv
from waitress import serve
from loguru import logger

# 載入環境變數
load_dotenv()

# 設置生產環境
os.environ['FLASK_ENV'] = 'production'

# 導入應用
from app import create_app

def main():
    """主函數"""
    try:
        # 創建應用
        app = create_app()
        
        # 配置參數
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', 5000))
        threads = int(os.environ.get('THREADS', 4))
        
        logger.info("="*50)
        logger.info("公司知識管理系統 - 生產環境")
        logger.info("="*50)
        logger.info(f"主機: {host}")
        logger.info(f"端口: {port}")
        logger.info(f"線程數: {threads}")
        logger.info(f"AI服務: {app.config.get('AI_SERVICE', 'openai')}")
        logger.info("="*50)
        
        # 啟動服務器
        serve(
            app,
            host=host,
            port=port,
            threads=threads,
            url_scheme='http',
            cleanup_interval=30,
            channel_timeout=120
        )
        
    except KeyboardInterrupt:
        logger.info("服務器已停止")
    except Exception as e:
        logger.error(f"服務器啟動失敗: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
