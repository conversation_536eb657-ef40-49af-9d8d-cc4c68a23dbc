# 部署指南

本文檔說明如何在Windows Server 2019上部署公司知識管理系統。

## 系統需求

### 硬體需求
- CPU: 4核心以上
- 記憶體: 8GB以上（推薦16GB）
- 硬碟: 100GB以上可用空間
- 網路: 穩定的網路連接

### 軟體需求
- Windows Server 2019或更新版本
- Python 3.8或更新版本
- Git（可選，用於代碼管理）

## 安裝步驟

### 1. 安裝Python

1. 下載Python 3.8+：https://www.python.org/downloads/windows/
2. 安裝時勾選"Add Python to PATH"
3. 驗證安裝：
   ```cmd
   python --version
   pip --version
   ```

### 2. 下載項目代碼

```cmd
# 如果有Git
git clone <repository-url>
cd knowledge-management

# 或直接解壓縮項目文件到目標目錄
```

### 3. 運行安裝腳本

```cmd
# Windows
install.bat

# 或手動安裝
pip install -r requirements.txt
```

### 4. 配置環境

1. 複製環境配置文件：
   ```cmd
   copy .env.example .env
   ```

2. 編輯 `.env` 文件，配置以下參數：
   ```env
   # Flask環境
   FLASK_ENV=production
   SECRET_KEY=your-very-secure-secret-key-here
   
   # AI服務配置（選擇其中一種）
   AI_SERVICE=openai
   OPENAI_API_KEY=your-openai-api-key
   
   # 或使用Azure OpenAI
   AI_SERVICE=azure
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_KEY=your-azure-openai-key
   
   # 或使用本地Ollama
   AI_SERVICE=ollama
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_MODEL=llama3
   
   # OCR配置（可選）
   TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
   
   # 服務器配置
   HOST=0.0.0.0
   PORT=5000
   THREADS=4
   ```

### 5. 安裝OCR支援（可選）

如需支援掃描PDF的OCR功能：

1. 下載Tesseract OCR：https://github.com/UB-Mannheim/tesseract/wiki
2. 安裝時選擇繁體中文語言包
3. 在 `.env` 中配置 `TESSERACT_PATH`

## 運行系統

### 開發模式
```cmd
python app.py
```

### 生產模式
```cmd
python run_production.py
```

### 作為Windows服務運行

1. 安裝NSSM（Non-Sucking Service Manager）：
   - 下載：https://nssm.cc/download
   - 解壓到 `C:\nssm`

2. 創建服務：
   ```cmd
   C:\nssm\nssm.exe install KnowledgeManagement
   ```

3. 配置服務：
   - Application: `C:\Python39\python.exe`
   - Arguments: `C:\path\to\knowledge-management\run_production.py`
   - Startup directory: `C:\path\to\knowledge-management`

4. 啟動服務：
   ```cmd
   net start KnowledgeManagement
   ```

## IIS部署（可選）

### 1. 安裝IIS和CGI

1. 開啟Windows功能：
   - Internet Information Services
   - CGI

### 2. 安裝wfastcgi

```cmd
pip install wfastcgi
wfastcgi-enable
```

### 3. 配置IIS站點

1. 創建新站點
2. 設置物理路徑為項目目錄
3. 配置web.config：

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="PythonHandler" path="*" verb="*" modules="FastCgiModule"
           scriptProcessor="C:\Python39\python.exe|C:\Python39\Lib\site-packages\wfastcgi.py"
           resourceType="Unspecified" requireAccess="Script" />
    </handlers>
  </system.webServer>
  <appSettings>
    <add key="PYTHONPATH" value="C:\path\to\knowledge-management" />
    <add key="WSGI_HANDLER" value="app.app" />
    <add key="WSGI_LOG" value="C:\path\to\knowledge-management\logs\iis.log" />
  </appSettings>
</configuration>
```

## 防火牆配置

開放端口5000（或您配置的端口）：

```cmd
netsh advfirewall firewall add rule name="Knowledge Management" dir=in action=allow protocol=TCP localport=5000
```

## 監控和維護

### 日誌文件位置
- 應用日誌：`logs/app.log`
- IIS日誌：`logs/iis.log`（如使用IIS）

### 定期維護任務

1. **清理日誌**：定期清理過期日誌文件
2. **備份數據**：備份 `uploads/` 和 `vector_db/` 目錄
3. **更新依賴**：定期更新Python依賴包
4. **監控磁碟空間**：確保有足夠的存儲空間

### 性能優化

1. **調整線程數**：根據CPU核心數調整 `THREADS` 參數
2. **記憶體監控**：監控向量數據庫的記憶體使用
3. **磁碟I/O**：考慮使用SSD存儲向量數據庫

## 故障排除

### 常見問題

1. **端口被占用**
   ```cmd
   netstat -ano | findstr :5000
   taskkill /PID <PID> /F
   ```

2. **Python模組找不到**
   - 檢查PYTHONPATH環境變數
   - 確認虛擬環境激活

3. **AI服務連接失敗**
   - 檢查API密鑰配置
   - 測試網路連接
   - 查看應用日誌

4. **OCR功能不工作**
   - 檢查Tesseract安裝路徑
   - 確認語言包已安裝

### 日誌分析

查看詳細錯誤信息：
```cmd
type logs\app.log | findstr ERROR
```

## 安全建議

1. **更改默認端口**：不要使用默認的5000端口
2. **設置強密碼**：為SECRET_KEY設置複雜密碼
3. **限制訪問**：配置防火牆規則限制訪問來源
4. **定期更新**：保持系統和依賴包更新
5. **備份策略**：制定定期備份計劃

## 擴展配置

### 負載均衡

如需支援更多用戶，可配置多個實例：

1. 運行多個應用實例（不同端口）
2. 使用Nginx或IIS作為反向代理
3. 配置負載均衡規則

### 數據庫優化

對於大量文檔，考慮：

1. 使用更強大的向量數據庫
2. 配置數據庫分片
3. 實施緩存策略

## 聯繫支援

如遇到部署問題，請提供：
1. 錯誤日誌
2. 系統配置信息
3. 重現步驟
