"""
文檔處理服務
負責PDF和DOCX文檔的文字提取、OCR處理和內容預處理
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from loguru import logger

# PDF處理
import PyPDF2
from pdf2image import convert_from_path
import pytesseract

# Word文檔處理
from docx import Document

# 文本處理
import hashlib


class DocumentProcessor:
    """文檔處理器"""
    
    def __init__(self, config):
        self.config = config
        self.tesseract_path = config.get('TESSERACT_PATH')
        if self.tesseract_path and os.path.exists(self.tesseract_path):
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_path
    
    def process_document(self, file_path: str, category: str) -> Dict:
        """
        處理文檔並提取文字內容
        
        Args:
            file_path: 文檔路徑
            category: 文檔分類
            
        Returns:
            包含處理結果的字典
        """
        try:
            file_extension = Path(file_path).suffix.lower()
            
            if file_extension == '.pdf':
                return self._process_pdf(file_path, category)
            elif file_extension == '.docx':
                return self._process_docx(file_path, category)
            else:
                raise ValueError(f"不支援的文件格式: {file_extension}")
                
        except Exception as e:
            logger.error(f"處理文檔失敗 {file_path}: {e}")
            raise
    
    def _process_pdf(self, file_path: str, category: str) -> Dict:
        """處理PDF文檔"""
        logger.info(f"開始處理PDF文檔: {file_path}")
        
        text_content = ""
        page_count = 0
        
        try:
            # 嘗試直接提取文字
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                page_count = len(pdf_reader.pages)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n--- 第 {page_num + 1} 頁 ---\n"
                            text_content += page_text
                    except Exception as e:
                        logger.warning(f"提取第 {page_num + 1} 頁文字失敗: {e}")
            
            # 如果提取的文字太少，嘗試OCR
            if len(text_content.strip()) < 100:
                logger.info("文字內容太少，嘗試OCR處理")
                ocr_text = self._perform_ocr(file_path)
                if ocr_text:
                    text_content = ocr_text
            
        except Exception as e:
            logger.error(f"PDF處理失敗: {e}")
            # 如果直接提取失敗，嘗試OCR
            text_content = self._perform_ocr(file_path)
        
        return self._create_document_result(file_path, category, text_content, page_count)
    
    def _process_docx(self, file_path: str, category: str) -> Dict:
        """處理DOCX文檔"""
        logger.info(f"開始處理DOCX文檔: {file_path}")
        
        try:
            doc = Document(file_path)
            text_content = ""
            paragraph_count = 0
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content += paragraph.text + "\n"
                    paragraph_count += 1
            
            # 處理表格內容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content += " | ".join(row_text) + "\n"
            
            logger.info(f"DOCX處理完成，共 {paragraph_count} 個段落")
            
        except Exception as e:
            logger.error(f"DOCX處理失敗: {e}")
            raise
        
        return self._create_document_result(file_path, category, text_content, paragraph_count)
    
    def _perform_ocr(self, file_path: str) -> str:
        """執行OCR文字識別"""
        if not self.tesseract_path or not os.path.exists(self.tesseract_path):
            logger.warning("Tesseract未配置或不存在，跳過OCR處理")
            return ""
        
        try:
            logger.info("開始OCR處理...")
            
            # 將PDF轉換為圖片
            images = convert_from_path(file_path, dpi=300)
            ocr_text = ""
            
            for i, image in enumerate(images):
                logger.info(f"OCR處理第 {i + 1} 頁")
                
                # 執行OCR
                page_text = pytesseract.image_to_string(
                    image, 
                    lang='chi_tra+eng',  # 繁體中文+英文
                    config='--psm 6'
                )
                
                if page_text.strip():
                    ocr_text += f"\n--- 第 {i + 1} 頁 (OCR) ---\n"
                    ocr_text += page_text
            
            logger.info(f"OCR處理完成，提取文字長度: {len(ocr_text)}")
            return ocr_text
            
        except Exception as e:
            logger.error(f"OCR處理失敗: {e}")
            return ""
    
    def _create_document_result(self, file_path: str, category: str, 
                              text_content: str, page_count: int) -> Dict:
        """創建文檔處理結果"""
        
        # 清理和預處理文字
        cleaned_text = self._clean_text(text_content)
        
        # 計算文檔MD5
        file_md5 = self._calculate_file_md5(file_path)
        
        # 文檔基本信息
        file_info = Path(file_path)
        
        result = {
            'file_path': file_path,
            'file_name': file_info.name,
            'file_size': file_info.stat().st_size,
            'file_md5': file_md5,
            'category': category,
            'text_content': cleaned_text,
            'text_length': len(cleaned_text),
            'page_count': page_count,
            'processed_at': self._get_current_timestamp()
        }
        
        logger.info(f"文檔處理完成: {file_info.name}, 文字長度: {len(cleaned_text)}")
        return result
    
    def _clean_text(self, text: str) -> str:
        """清理和預處理文字內容"""
        if not text:
            return ""
        
        # 移除多餘的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、數字、基本標點）
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,;:!?()[\]{}"\'-]', ' ', text)
        
        # 移除多餘的換行
        text = re.sub(r'\n\s*\n', '\n', text)
        
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    def _calculate_file_md5(self, file_path: str) -> str:
        """計算文件MD5值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _get_current_timestamp(self) -> str:
        """獲取當前時間戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def split_text_into_chunks(self, text: str, chunk_size: int = 600, 
                              overlap: int = 125) -> List[Dict]:
        """
        將文字分割為可檢索的片段
        
        Args:
            text: 原始文字
            chunk_size: 每個片段的字數
            overlap: 片段重疊字數
            
        Returns:
            文字片段列表
        """
        if not text or len(text) < chunk_size:
            return [{'text': text, 'chunk_id': 0, 'start_pos': 0, 'end_pos': len(text)}]
        
        chunks = []
        start = 0
        chunk_id = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            
            # 嘗試在句號、問號、驚嘆號處分割
            if end < len(text):
                for i in range(end, max(start + chunk_size - 100, start), -1):
                    if text[i] in '。！？\n':
                        end = i + 1
                        break
            
            chunk_text = text[start:end].strip()
            if chunk_text:
                chunks.append({
                    'text': chunk_text,
                    'chunk_id': chunk_id,
                    'start_pos': start,
                    'end_pos': end
                })
                chunk_id += 1
            
            # 計算下一個片段的起始位置（考慮重疊）
            start = max(start + 1, end - overlap)
            
            # 避免無限循環
            if start >= len(text):
                break
        
        logger.info(f"文字分割完成，共 {len(chunks)} 個片段")
        return chunks
