#!/bin/bash

echo "========================================"
echo "公司知識管理系統 - 安裝腳本"
echo "========================================"

# 檢查Python版本
echo ""
echo "1. 檢查Python版本..."
if ! command -v python3 &> /dev/null; then
    echo "錯誤: 未找到Python3，請先安裝Python 3.8或更高版本"
    exit 1
fi

python3 --version

# 檢查pip
echo ""
echo "2. 升級pip..."
python3 -m pip install --upgrade pip

# 安裝依賴
echo ""
echo "3. 安裝Python依賴..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "錯誤: 依賴安裝失敗"
    exit 1
fi

# 創建環境配置文件
echo ""
echo "4. 創建環境配置文件..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "已創建 .env 文件，請編輯配置您的設定"
else
    echo ".env 文件已存在"
fi

# 創建必要目錄
echo ""
echo "5. 創建必要目錄..."
mkdir -p uploads/{technical,contract,customer_service,others}
mkdir -p logs
mkdir -p vector_db

# 設置權限
chmod +x app.py
chmod +x run_production.py

echo ""
echo "========================================"
echo "安裝完成！"
echo "========================================"
echo ""
echo "下一步："
echo "1. 編輯 .env 文件配置您的設定"
echo "2. 運行 python3 app.py 啟動開發服務器"
echo "3. 或運行 python3 run_production.py 啟動生產服務器"
echo ""
