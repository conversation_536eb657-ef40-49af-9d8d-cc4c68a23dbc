from flask import Blueprint, render_template, current_app
from loguru import logger

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主頁面"""
    try:
        categories = current_app.config['DOCUMENT_CATEGORIES']
        return render_template('index.html', categories=categories)
    except Exception as e:
        logger.error(f"Error loading main page: {e}")
        return render_template('error.html', error="頁面載入失敗"), 500

@main_bp.route('/health')
def health_check():
    """健康檢查端點"""
    return {
        'status': 'healthy',
        'service': 'knowledge-management-system',
        'version': '1.0.0'
    }

@main_bp.route('/management')
def management():
    """管理頁面"""
    try:
        categories = current_app.config['DOCUMENT_CATEGORIES']
        return render_template('management.html', categories=categories)
    except Exception as e:
        logger.error(f"Error loading management page: {e}")
        return render_template('error.html', error="管理頁面載入失敗"), 500

@main_bp.route('/config')
def get_config():
    """獲取前端需要的配置信息"""
    try:
        return {
            'categories': current_app.config['DOCUMENT_CATEGORIES'],
            'max_file_size': current_app.config['MAX_CONTENT_LENGTH'],
            'allowed_extensions': list(current_app.config['ALLOWED_EXTENSIONS']),
            'ai_service': current_app.config['AI_SERVICE']
        }
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        return {'error': 'Failed to get configuration'}, 500
