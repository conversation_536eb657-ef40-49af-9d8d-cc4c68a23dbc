/* 自定義樣式 */

body {
    background-color: #f8f9fa;
    font-family: 'Microsoft JhengHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: border-color 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 搜索結果樣式 */
.search-result-item {
    border-left: 4px solid #0d6efd;
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 6px 6px 0;
}

.source-item {
    background-color: #e9ecef;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    border-left: 3px solid #6c757d;
}

.source-item:hover {
    background-color: #dee2e6;
    cursor: pointer;
}

/* 上傳區域樣式 */
.upload-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-drop-zone:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.upload-drop-zone.dragover {
    border-color: #0d6efd;
    background-color: #e3f2fd;
}

/* 進度條樣式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

/* 載入動畫 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 文字高亮 */
.highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

/* 來源引用樣式 */
.citation {
    color: #0d6efd;
    font-weight: 600;
    text-decoration: none;
}

.citation:hover {
    text-decoration: underline;
}

/* 成功/錯誤訊息 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
}

/* 導航欄樣式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

/* 頁腳樣式 */
footer {
    margin-top: auto;
    border-top: 1px solid #dee2e6;
}
