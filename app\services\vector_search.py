"""
向量檢索服務
使用ChromaDB和Sentence Transformers實現語義檢索
"""

import os
import uuid
from typing import List, Dict, Optional, Tuple
from loguru import logger

# 向量數據庫
import chromadb
from chromadb.config import Settings

# 文本嵌入
from sentence_transformers import SentenceTransformer

# 數據模型
from app.models.document import Document, DocumentChunk


class VectorSearchService:
    """向量檢索服務"""
    
    def __init__(self, config):
        self.config = config
        self.db_path = config.get('VECTOR_DB_PATH', './vector_db')
        self.collection_name = "knowledge_base"
        
        # 初始化ChromaDB
        self._init_chroma_db()
        
        # 初始化嵌入模型
        self._init_embedding_model()
        
        # 獲取或創建集合
        self._init_collection()
    
    def _init_chroma_db(self):
        """初始化ChromaDB"""
        try:
            # 確保數據庫目錄存在
            os.makedirs(self.db_path, exist_ok=True)
            
            # 創建ChromaDB客戶端
            self.chroma_client = chromadb.PersistentClient(
                path=self.db_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            logger.info(f"ChromaDB初始化成功，數據庫路徑: {self.db_path}")
            
        except Exception as e:
            logger.error(f"ChromaDB初始化失敗: {e}")
            raise
    
    def _init_embedding_model(self):
        """初始化嵌入模型"""
        try:
            # 使用多語言模型，支援中文和英文
            model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            
            logger.info(f"載入嵌入模型: {model_name}")
            self.embedding_model = SentenceTransformer(model_name)
            
            # 測試模型
            test_embedding = self.embedding_model.encode("測試文本")
            self.embedding_dimension = len(test_embedding)
            
            logger.info(f"嵌入模型載入成功，維度: {self.embedding_dimension}")
            
        except Exception as e:
            logger.error(f"嵌入模型初始化失敗: {e}")
            raise
    
    def _init_collection(self):
        """初始化或獲取集合"""
        try:
            # 嘗試獲取現有集合
            try:
                self.collection = self.chroma_client.get_collection(
                    name=self.collection_name
                )
                logger.info(f"獲取現有集合: {self.collection_name}")
            except:
                # 創建新集合
                self.collection = self.chroma_client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "公司知識管理系統文檔集合"}
                )
                logger.info(f"創建新集合: {self.collection_name}")
            
            # 獲取集合統計信息
            count = self.collection.count()
            logger.info(f"集合中現有文檔片段數量: {count}")
            
        except Exception as e:
            logger.error(f"集合初始化失敗: {e}")
            raise
    
    def add_document(self, document: Document, chunks: List[Dict]) -> bool:
        """
        添加文檔到向量數據庫
        
        Args:
            document: 文檔對象
            chunks: 文檔片段列表
            
        Returns:
            是否添加成功
        """
        try:
            if not chunks:
                logger.warning(f"文檔 {document.file_name} 沒有文字片段")
                return False
            
            # 檢查文檔是否已存在
            if self._document_exists(document.doc_id):
                logger.warning(f"文檔已存在於向量數據庫: {document.file_name}")
                return False
            
            # 準備數據
            texts = []
            metadatas = []
            ids = []
            
            for chunk in chunks:
                chunk_id = f"{document.doc_id}_{chunk['chunk_id']}"
                
                texts.append(chunk['text'])
                metadatas.append({
                    'doc_id': document.doc_id,
                    'file_name': document.file_name,
                    'file_path': document.file_path,
                    'category': document.category,
                    'chunk_id': chunk['chunk_id'],
                    'start_pos': chunk['start_pos'],
                    'end_pos': chunk['end_pos'],
                    'file_size': document.file_size,
                    'processed_at': document.processed_at
                })
                ids.append(chunk_id)
            
            # 生成嵌入向量
            logger.info(f"為文檔 {document.file_name} 生成 {len(texts)} 個嵌入向量")
            embeddings = self.embedding_model.encode(texts).tolist()
            
            # 添加到集合
            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"文檔 {document.file_name} 成功添加到向量數據庫")
            return True
            
        except Exception as e:
            logger.error(f"添加文檔到向量數據庫失敗: {e}")
            return False
    
    def search(self, query: str, category: str = "all", 
               top_k: int = 5) -> List[Dict]:
        """
        執行語義檢索
        
        Args:
            query: 查詢文本
            category: 文檔分類，"all"表示所有分類
            top_k: 返回結果數量
            
        Returns:
            檢索結果列表
        """
        try:
            if not query.strip():
                return []
            
            # 生成查詢向量
            query_embedding = self.embedding_model.encode([query]).tolist()
            
            # 構建過濾條件
            where_filter = None
            if category != "all":
                where_filter = {"category": category}
            
            # 執行檢索
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=top_k,
                where=where_filter,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 處理結果
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    result = {
                        'text': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i],
                        'relevance_score': 1 - results['distances'][0][i]  # 轉換為相關度分數
                    }
                    search_results.append(result)
            
            logger.info(f"檢索完成，查詢: '{query}'，分類: '{category}'，結果數: {len(search_results)}")
            return search_results
            
        except Exception as e:
            logger.error(f"向量檢索失敗: {e}")
            return []
    
    def _document_exists(self, doc_id: str) -> bool:
        """檢查文檔是否已存在"""
        try:
            results = self.collection.get(
                where={"doc_id": doc_id},
                limit=1
            )
            return len(results['ids']) > 0
        except:
            return False
    
    def remove_document(self, doc_id: str) -> bool:
        """
        從向量數據庫中移除文檔
        
        Args:
            doc_id: 文檔ID
            
        Returns:
            是否移除成功
        """
        try:
            # 獲取文檔的所有片段ID
            results = self.collection.get(
                where={"doc_id": doc_id}
            )
            
            if not results['ids']:
                logger.warning(f"文檔不存在於向量數據庫: {doc_id}")
                return False
            
            # 刪除所有片段
            self.collection.delete(ids=results['ids'])
            
            logger.info(f"從向量數據庫移除文檔: {doc_id}，共 {len(results['ids'])} 個片段")
            return True
            
        except Exception as e:
            logger.error(f"從向量數據庫移除文檔失敗: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """獲取向量數據庫統計信息"""
        try:
            total_chunks = self.collection.count()
            
            # 獲取所有元數據來統計
            all_metadata = self.collection.get(include=['metadatas'])
            
            categories = {}
            documents = set()
            
            for metadata in all_metadata['metadatas']:
                # 統計分類
                category = metadata.get('category', 'unknown')
                categories[category] = categories.get(category, 0) + 1
                
                # 統計文檔數
                documents.add(metadata.get('doc_id'))
            
            return {
                'total_chunks': total_chunks,
                'total_documents': len(documents),
                'categories': categories,
                'embedding_dimension': self.embedding_dimension
            }
            
        except Exception as e:
            logger.error(f"獲取統計信息失敗: {e}")
            return {}
    
    def reset_database(self) -> bool:
        """重置向量數據庫"""
        try:
            # 刪除現有集合
            self.chroma_client.delete_collection(self.collection_name)
            
            # 重新創建集合
            self._init_collection()
            
            logger.info("向量數據庫重置成功")
            return True
            
        except Exception as e:
            logger.error(f"重置向量數據庫失敗: {e}")
            return False
