// 文件上傳功能 JavaScript

$(document).ready(function() {
    // 上傳表單提交
    $('#uploadForm').on('submit', function(e) {
        e.preventDefault();
        uploadFile();
    });
    
    // 文件選擇變化
    $('#fileInput').on('change', function() {
        validateSelectedFile();
    });
    
    // 拖拽上傳功能
    setupDragAndDrop();
});

// 上傳文件
function uploadFile() {
    const fileInput = $('#fileInput')[0];
    const categorySelect = $('#uploadCategory');
    const file = fileInput.files[0];
    const category = categorySelect.val();
    
    // 驗證輸入
    if (!file) {
        showAlert('請選擇要上傳的文件', 'warning');
        return;
    }
    
    if (!category) {
        showAlert('請選擇文件分類', 'warning');
        categorySelect.focus();
        return;
    }
    
    // 驗證文件
    if (!validateFile(file)) {
        return;
    }
    
    // 準備上傳
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);
    
    // 顯示上傳進度
    showUploadProgress();
    $('#uploadBtn').prop('disabled', true).html('<span class="loading-spinner"></span> 上傳中...');
    
    // 執行上傳
    $.ajax({
        url: '/api/upload',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            // 上傳進度
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    updateUploadProgress(percentComplete);
                }
            }, false);
            return xhr;
        },
        success: function(response) {
            hideUploadProgress();
            showUploadResult(response, 'success');
            resetUploadForm();
        },
        error: function(xhr, status, error) {
            console.error('上傳錯誤:', error);
            hideUploadProgress();
            
            let errorMessage = '文件上傳失敗，請稍後再試';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            
            showUploadResult({ error: errorMessage }, 'error');
        },
        complete: function() {
            $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload me-1"></i>上傳文檔');
        }
    });
}

// 驗證選中的文件
function validateSelectedFile() {
    const fileInput = $('#fileInput')[0];
    const file = fileInput.files[0];
    
    if (!file) return;
    
    if (!validateFile(file)) {
        fileInput.value = '';
        return false;
    }
    
    // 顯示文件信息
    const fileInfo = `
        <div class="alert alert-info mt-2">
            <i class="fas fa-file me-2"></i>
            <strong>${file.name}</strong> (${formatFileSize(file.size)})
        </div>
    `;
    $('#uploadResult').html(fileInfo);
    
    return true;
}

// 驗證文件
function validateFile(file) {
    // 檢查文件類型
    if (!validateFileType(file)) {
        showAlert('不支援的文件格式，僅支援 PDF 和 DOCX 格式', 'danger');
        return false;
    }
    
    // 檢查文件大小
    if (!validateFileSize(file)) {
        showAlert(`文件過大，最大允許 ${formatFileSize(50 * 1024 * 1024)}`, 'danger');
        return false;
    }
    
    return true;
}

// 顯示上傳進度
function showUploadProgress() {
    $('#uploadProgress').show();
    updateUploadProgress(0);
}

// 更新上傳進度
function updateUploadProgress(percent) {
    const progressBar = $('#uploadProgress .progress-bar');
    progressBar.css('width', percent + '%');
    progressBar.attr('aria-valuenow', percent);
    
    if (percent < 100) {
        progressBar.text(`${Math.round(percent)}%`);
    } else {
        progressBar.text('處理中...');
    }
}

// 隱藏上傳進度
function hideUploadProgress() {
    $('#uploadProgress').hide();
}

// 顯示上傳結果
function showUploadResult(response, type) {
    let resultHtml = '';
    
    if (type === 'success') {
        resultHtml = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>上傳成功！</strong><br>
                文件名：${response.filename}<br>
                分類：${getCategoryName(response.category)}<br>
                MD5：<code>${response.md5}</code>
                <button class="btn btn-sm btn-outline-success ms-2" onclick="copyToClipboard('${response.md5}')">
                    <i class="fas fa-copy"></i> 複製MD5
                </button>
            </div>
        `;
        
        // 顯示成功通知
        showAlert('文件上傳成功，正在建立索引...', 'success');
        
    } else {
        resultHtml = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>上傳失敗：</strong>${response.error}
            </div>
        `;
    }
    
    $('#uploadResult').html(resultHtml);
}

// 重置上傳表單
function resetUploadForm() {
    $('#uploadForm')[0].reset();
    setTimeout(() => {
        $('#uploadResult').fadeOut(() => {
            $('#uploadResult').html('');
        });
    }, 5000);
}

// 設置拖拽上傳
function setupDragAndDrop() {
    const uploadArea = $('#uploadForm .card-body');
    
    // 防止默認拖拽行為
    $(document).on('dragover drop', function(e) {
        e.preventDefault();
    });
    
    // 拖拽進入
    uploadArea.on('dragenter dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    // 拖拽離開
    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    // 文件放下
    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            
            // 設置文件到input
            const fileInput = $('#fileInput')[0];
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
            
            // 觸發change事件
            $('#fileInput').trigger('change');
        }
    });
}

// 獲取分類名稱（與search.js中的函數相同）
function getCategoryName(category) {
    const categories = {
        'technical': '技術類',
        'contract': '業務合約類',
        'customer_service': '客服類',
        'others': '其他類'
    };
    return categories[category] || category;
}
