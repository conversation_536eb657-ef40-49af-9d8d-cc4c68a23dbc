from flask import Flask
from flask_cors import CORS
import os
from pathlib import Path
from loguru import logger

def create_app(config_name='default'):
    """應用工廠函數"""
    app = Flask(__name__)
    
    # 載入配置
    from config import get_config
    app.config.from_object(get_config())
    
    # 啟用CORS
    CORS(app)
    
    # 確保必要目錄存在
    ensure_directories(app)
    
    # 配置日誌
    setup_logging(app)
    
    # 註冊藍圖
    register_blueprints(app)
    
    # 錯誤處理
    register_error_handlers(app)
    
    return app

def ensure_directories(app):
    """確保必要的目錄存在"""
    directories = [
        app.config['UPLOAD_FOLDER'],
        app.config['VECTOR_DB_PATH'],
        os.path.dirname(app.config['LOG_FILE']),
        os.path.join(app.config['UPLOAD_FOLDER'], 'technical'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'contract'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'customer_service'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'others'),
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def setup_logging(app):
    """配置日誌"""
    log_level = app.config.get('LOG_LEVEL', 'INFO')
    log_file = app.config.get('LOG_FILE')
    
    # 移除默認處理器
    logger.remove()
    
    # 添加控制台輸出
    logger.add(
        lambda msg: print(msg, end=""),
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件輸出
    if log_file:
        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="30 days"
        )

def register_blueprints(app):
    """註冊藍圖"""
    from app.routes.main import main_bp
    from app.routes.upload import upload_bp
    from app.routes.search import search_bp
    from app.routes.management import management_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(upload_bp, url_prefix='/api')
    app.register_blueprint(search_bp, url_prefix='/api')
    app.register_blueprint(management_bp, url_prefix='/api')

def register_error_handlers(app):
    """註冊錯誤處理器"""
    @app.errorhandler(404)
    def not_found_error(error):
        return {'error': 'Not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"Internal server error: {error}")
        return {'error': 'Internal server error'}, 500
    
    @app.errorhandler(413)
    def too_large(error):
        return {'error': 'File too large'}, 413
