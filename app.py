#!/usr/bin/env python3
"""
公司知識管理系統主程序
"""

import os
from dotenv import load_dotenv
from app import create_app
from loguru import logger

# 載入環境變數
load_dotenv()

# 創建Flask應用
app = create_app()

if __name__ == '__main__':
    # 開發模式運行
    logger.info("啟動知識管理系統...")
    logger.info(f"環境: {os.environ.get('FLASK_ENV', 'development')}")
    logger.info(f"AI服務: {app.config.get('AI_SERVICE', 'openai')}")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config.get('DEBUG', False)
    )
