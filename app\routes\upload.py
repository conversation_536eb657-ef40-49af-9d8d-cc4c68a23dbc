from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
import os
import hashlib
from loguru import logger

from app.services.indexing_service import IndexingService
from app.utils.file_utils import calculate_file_md5, is_valid_file_type, get_safe_filename

upload_bp = Blueprint('upload', __name__)

# 全局索引服務實例
_indexing_service = None

def get_indexing_service():
    """獲取索引服務實例"""
    global _indexing_service
    if _indexing_service is None:
        _indexing_service = IndexingService(current_app.config)
    return _indexing_service

@upload_bp.route('/upload', methods=['POST'])
def upload_file():
    """文件上傳端點"""
    try:
        # 檢查是否有文件
        if 'file' not in request.files:
            return jsonify({'error': '沒有選擇文件'}), 400

        file = request.files['file']
        category = request.form.get('category')

        # 檢查文件名
        if file.filename == '':
            return jsonify({'error': '沒有選擇文件'}), 400

        # 檢查分類
        if category not in current_app.config['DOCUMENT_CATEGORIES']:
            return jsonify({'error': '無效的文件分類'}), 400

        # 檢查文件格式
        if not is_valid_file_type(file.filename, current_app.config['ALLOWED_EXTENSIONS']):
            return jsonify({'error': '不支援的文件格式，僅支援PDF和DOCX'}), 400

        # 安全的文件名
        filename = get_safe_filename(file.filename)

        # 創建分類目錄路徑
        category_path = os.path.join(current_app.config['UPLOAD_FOLDER'], category)
        os.makedirs(category_path, exist_ok=True)

        # 保存文件
        file_path = os.path.join(category_path, filename)
        file.save(file_path)

        # 計算MD5
        file_md5 = calculate_file_md5(file_path)

        logger.info(f"File uploaded: {filename}, category: {category}, MD5: {file_md5}")

        # 執行文檔索引
        indexing_service = get_indexing_service()
        index_result = indexing_service.index_document(file_path, category)

        if index_result['success']:
            return jsonify({
                'message': '文件上傳並索引成功',
                'filename': filename,
                'category': category,
                'md5': file_md5,
                'path': file_path,
                'chunks_count': index_result.get('chunks_count', 0)
            }), 200
        else:
            # 如果索引失敗，刪除已上傳的文件
            if os.path.exists(file_path):
                os.remove(file_path)

            return jsonify({
                'error': f'文件索引失敗: {index_result["message"]}'
            }), 500

    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return jsonify({'error': '文件上傳失敗'}), 500

@upload_bp.route('/upload/batch', methods=['POST'])
def batch_upload():
    """批量文件上傳"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': '沒有選擇文件'}), 400

        files = request.files.getlist('files')
        category = request.form.get('category')

        if not category or category not in current_app.config['DOCUMENT_CATEGORIES']:
            return jsonify({'error': '無效的文件分類'}), 400

        if not files or len(files) == 0:
            return jsonify({'error': '沒有選擇文件'}), 400

        results = []
        success_count = 0
        error_count = 0

        indexing_service = get_indexing_service()

        for file in files:
            if file.filename == '':
                continue

            try:
                # 檢查文件格式
                if not is_valid_file_type(file.filename, current_app.config['ALLOWED_EXTENSIONS']):
                    results.append({
                        'filename': file.filename,
                        'success': False,
                        'message': '不支援的文件格式'
                    })
                    error_count += 1
                    continue

                # 安全的文件名
                filename = get_safe_filename(file.filename)

                # 創建分類目錄路徑
                category_path = os.path.join(current_app.config['UPLOAD_FOLDER'], category)
                os.makedirs(category_path, exist_ok=True)

                # 保存文件
                file_path = os.path.join(category_path, filename)
                file.save(file_path)

                # 計算MD5
                file_md5 = calculate_file_md5(file_path)

                # 執行文檔索引
                index_result = indexing_service.index_document(file_path, category)

                if index_result['success']:
                    results.append({
                        'filename': filename,
                        'success': True,
                        'md5': file_md5,
                        'chunks_count': index_result.get('chunks_count', 0)
                    })
                    success_count += 1
                else:
                    # 如果索引失敗，刪除已上傳的文件
                    if os.path.exists(file_path):
                        os.remove(file_path)

                    results.append({
                        'filename': filename,
                        'success': False,
                        'message': index_result['message']
                    })
                    error_count += 1

            except Exception as e:
                logger.error(f"批量上傳處理文件失敗 {file.filename}: {e}")
                results.append({
                    'filename': file.filename,
                    'success': False,
                    'message': f'處理失敗: {str(e)}'
                })
                error_count += 1

        logger.info(f"批量上傳完成，成功: {success_count}，失敗: {error_count}")

        return jsonify({
            'message': f'批量上傳完成，成功: {success_count}，失敗: {error_count}',
            'success_count': success_count,
            'error_count': error_count,
            'results': results
        }), 200

    except Exception as e:
        logger.error(f"批量上傳失敗: {e}")
        return jsonify({'error': '批量上傳失敗'}), 500

@upload_bp.route('/upload/status/<file_id>')
def upload_status(file_id):
    """查詢文件上傳和處理狀態"""
    try:
        indexing_service = get_indexing_service()
        document = indexing_service.document_manager.get_document(file_id)

        if document:
            return jsonify({
                'file_id': file_id,
                'status': 'completed',
                'message': '文件處理完成',
                'document': document.to_dict()
            })
        else:
            return jsonify({
                'file_id': file_id,
                'status': 'not_found',
                'message': '文件不存在'
            })

    except Exception as e:
        logger.error(f"查詢文件狀態失敗: {e}")
        return jsonify({
            'file_id': file_id,
            'status': 'error',
            'message': '查詢失敗'
        })
