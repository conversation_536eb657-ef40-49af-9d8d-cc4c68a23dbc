# Flask 環境設定
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# AI 服務設定 (選擇其中一種)
AI_SERVICE=openai

# OpenAI 設定
OPENAI_API_KEY=your-openai-api-key

# Azure OpenAI 設定
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_KEY=your-azure-openai-key

# Ollama 本地模型設定
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3

# OCR 設定 (Windows)
TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe

# 日誌設定
LOG_LEVEL=INFO
