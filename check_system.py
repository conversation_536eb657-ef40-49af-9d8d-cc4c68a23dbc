#!/usr/bin/env python3
"""
系統檢查腳本
檢查系統配置和依賴是否正確
"""

import os
import sys
import importlib
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def check_python_version():
    """檢查Python版本"""
    print("檢查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python版本過低: {version.major}.{version.minor}.{version.micro}")
        print("  需要Python 3.8或更高版本")
        return False

def check_dependencies():
    """檢查Python依賴"""
    print("\n檢查Python依賴...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'PyPDF2',
        'python-docx',
        'chromadb',
        'sentence-transformers',
        'numpy',
        'requests',
        'waitress',
        'loguru'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依賴包: {', '.join(missing_packages)}")
        print("請運行: pip install -r requirements.txt")
        return False
    
    return True

def check_optional_dependencies():
    """檢查可選依賴"""
    print("\n檢查可選依賴...")
    
    optional_packages = [
        ('pytesseract', 'OCR功能'),
        ('pdf2image', 'PDF圖像轉換'),
        ('openai', 'OpenAI API')
    ]
    
    for package, description in optional_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"✓ {package} ({description})")
        except ImportError:
            print(f"- {package} ({description}) - 未安裝")

def check_directories():
    """檢查必要目錄"""
    print("\n檢查目錄結構...")
    
    required_dirs = [
        'uploads',
        'uploads/technical',
        'uploads/contract', 
        'uploads/customer_service',
        'uploads/others',
        'logs',
        'vector_db'
    ]
    
    all_exist = True
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path}")
            all_exist = False
            # 嘗試創建目錄
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"  已創建目錄: {dir_path}")
            except Exception as e:
                print(f"  創建目錄失敗: {e}")
    
    return all_exist

def check_config():
    """檢查配置文件"""
    print("\n檢查配置...")
    
    if not os.path.exists('.env'):
        print("✗ .env 文件不存在")
        if os.path.exists('.env.example'):
            print("  請複製 .env.example 為 .env 並配置")
        return False
    
    print("✓ .env 文件存在")
    
    # 檢查重要配置項
    important_configs = [
        'SECRET_KEY',
        'AI_SERVICE'
    ]
    
    missing_configs = []
    
    for config in important_configs:
        value = os.environ.get(config)
        if value:
            print(f"✓ {config}: {'*' * min(len(value), 10)}")
        else:
            print(f"✗ {config}: 未設置")
            missing_configs.append(config)
    
    # 檢查AI服務配置
    ai_service = os.environ.get('AI_SERVICE', 'openai')
    print(f"\nAI服務配置: {ai_service}")
    
    if ai_service == 'openai':
        api_key = os.environ.get('OPENAI_API_KEY')
        if api_key:
            print("✓ OpenAI API密鑰已配置")
        else:
            print("✗ OpenAI API密鑰未配置")
            missing_configs.append('OPENAI_API_KEY')
    
    elif ai_service == 'azure':
        endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT')
        key = os.environ.get('AZURE_OPENAI_KEY')
        if endpoint and key:
            print("✓ Azure OpenAI配置完整")
        else:
            print("✗ Azure OpenAI配置不完整")
            if not endpoint:
                missing_configs.append('AZURE_OPENAI_ENDPOINT')
            if not key:
                missing_configs.append('AZURE_OPENAI_KEY')
    
    elif ai_service == 'ollama':
        base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')
        print(f"✓ Ollama配置: {base_url}")
    
    return len(missing_configs) == 0

def check_tesseract():
    """檢查Tesseract OCR"""
    print("\n檢查Tesseract OCR...")
    
    tesseract_path = os.environ.get('TESSERACT_PATH')
    
    if tesseract_path and os.path.exists(tesseract_path):
        print(f"✓ Tesseract路徑: {tesseract_path}")
        return True
    else:
        # 嘗試在系統PATH中找到
        try:
            result = subprocess.run(['tesseract', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Tesseract在系統PATH中")
                return True
        except FileNotFoundError:
            pass
        
        print("- Tesseract OCR未找到（可選功能）")
        print("  如需OCR功能，請安裝Tesseract並配置路徑")
        return False

def check_ports():
    """檢查端口可用性"""
    print("\n檢查端口...")
    
    import socket
    
    port = int(os.environ.get('PORT', 5000))
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            print(f"✓ 端口 {port} 可用")
            return True
    except OSError:
        print(f"✗ 端口 {port} 被占用")
        return False

def test_app_import():
    """測試應用導入"""
    print("\n測試應用導入...")
    
    try:
        from app import create_app
        app = create_app()
        print("✓ 應用創建成功")
        return True
    except Exception as e:
        print(f"✗ 應用創建失敗: {e}")
        return False

def main():
    """主函數"""
    print("="*50)
    print("公司知識管理系統 - 系統檢查")
    print("="*50)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_optional_dependencies,
        check_directories,
        check_config,
        check_tesseract,
        check_ports,
        test_app_import
    ]
    
    results = []
    
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"檢查過程中發生錯誤: {e}")
            results.append(False)
    
    print("\n" + "="*50)
    print("檢查結果摘要")
    print("="*50)
    
    passed = sum(1 for r in results if r is True)
    total = len([r for r in results if r is not None])
    
    print(f"通過: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有檢查通過，系統準備就緒！")
        print("\n可以運行以下命令啟動系統:")
        print("  開發模式: python app.py")
        print("  生產模式: python run_production.py")
    else:
        print("✗ 部分檢查未通過，請修復問題後重新檢查")
    
    print("="*50)

if __name__ == '__main__':
    main()
