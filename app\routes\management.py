"""
文檔管理API端點
提供文檔列表、刪除、統計等管理功能
"""

from flask import Blueprint, request, jsonify, current_app
from loguru import logger

from app.services.indexing_service import IndexingService

management_bp = Blueprint('management', __name__)

# 全局索引服務實例
_indexing_service = None

def get_indexing_service():
    """獲取索引服務實例"""
    global _indexing_service
    if _indexing_service is None:
        _indexing_service = IndexingService(current_app.config)
    return _indexing_service

@management_bp.route('/documents', methods=['GET'])
def list_documents():
    """獲取文檔列表"""
    try:
        category = request.args.get('category', 'all')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        indexing_service = get_indexing_service()
        
        if category == 'all':
            documents = indexing_service.document_manager.get_all_documents()
        else:
            documents = indexing_service.document_manager.get_documents_by_category(category)
        
        # 分頁處理
        total = len(documents)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_docs = documents[start:end]
        
        # 格式化文檔信息
        doc_list = []
        for doc in paginated_docs:
            doc_info = doc.to_dict()
            doc_info['category_name'] = current_app.config['DOCUMENT_CATEGORIES'].get(
                doc.category, doc.category
            )
            doc_list.append(doc_info)
        
        return jsonify({
            'documents': doc_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        }), 200
        
    except Exception as e:
        logger.error(f"獲取文檔列表失敗: {e}")
        return jsonify({'error': '獲取文檔列表失敗'}), 500

@management_bp.route('/documents/<doc_id>', methods=['DELETE'])
def delete_document(doc_id):
    """刪除文檔"""
    try:
        indexing_service = get_indexing_service()
        
        # 獲取文檔信息
        document = indexing_service.document_manager.get_document(doc_id)
        if not document:
            return jsonify({'error': '文檔不存在'}), 404
        
        # 執行刪除
        result = indexing_service.remove_document(doc_id)
        
        if result['success']:
            logger.info(f"文檔刪除成功: {document.file_name}")
            return jsonify({
                'message': '文檔刪除成功',
                'file_name': document.file_name
            }), 200
        else:
            return jsonify({'error': result['message']}), 500
            
    except Exception as e:
        logger.error(f"刪除文檔失敗: {e}")
        return jsonify({'error': '刪除文檔失敗'}), 500

@management_bp.route('/documents/<doc_id>', methods=['GET'])
def get_document_detail(doc_id):
    """獲取文檔詳細信息"""
    try:
        indexing_service = get_indexing_service()
        document = indexing_service.document_manager.get_document(doc_id)
        
        if not document:
            return jsonify({'error': '文檔不存在'}), 404
        
        doc_info = document.to_dict()
        doc_info['category_name'] = current_app.config['DOCUMENT_CATEGORIES'].get(
            document.category, document.category
        )
        
        return jsonify(doc_info), 200
        
    except Exception as e:
        logger.error(f"獲取文檔詳情失敗: {e}")
        return jsonify({'error': '獲取文檔詳情失敗'}), 500

@management_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """獲取系統統計信息"""
    try:
        indexing_service = get_indexing_service()
        stats = indexing_service.get_statistics()
        
        # 添加分類名稱映射
        if 'documents' in stats and 'categories' in stats['documents']:
            category_stats = {}
            for category, count in stats['documents']['categories'].items():
                category_name = current_app.config['DOCUMENT_CATEGORIES'].get(category, category)
                category_stats[category_name] = count
            stats['documents']['category_names'] = category_stats
        
        return jsonify(stats), 200
        
    except Exception as e:
        logger.error(f"獲取統計信息失敗: {e}")
        return jsonify({'error': '獲取統計信息失敗'}), 500

@management_bp.route('/rebuild-index', methods=['POST'])
def rebuild_index():
    """重建索引"""
    try:
        indexing_service = get_indexing_service()
        result = indexing_service.rebuild_index()
        
        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify({'error': result['message']}), 500
            
    except Exception as e:
        logger.error(f"重建索引失敗: {e}")
        return jsonify({'error': '重建索引失敗'}), 500

@management_bp.route('/categories', methods=['GET'])
def get_categories():
    """獲取文檔分類列表"""
    try:
        categories = []
        for key, name in current_app.config['DOCUMENT_CATEGORIES'].items():
            categories.append({
                'key': key,
                'name': name
            })
        
        return jsonify({'categories': categories}), 200
        
    except Exception as e:
        logger.error(f"獲取分類列表失敗: {e}")
        return jsonify({'error': '獲取分類列表失敗'}), 500

@management_bp.route('/health', methods=['GET'])
def health_check():
    """健康檢查"""
    try:
        indexing_service = get_indexing_service()
        stats = indexing_service.get_statistics()
        
        return jsonify({
            'status': 'healthy',
            'service': 'document-management',
            'total_documents': stats.get('documents', {}).get('total_documents', 0),
            'vector_db_status': 'connected' if stats.get('vector_database') else 'disconnected'
        }), 200
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500
