import os
from pathlib import Path

class Config:
    """基礎配置類"""
    # 基本設定
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 檔案上傳設定
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'pdf', 'docx'}
    
    # 文檔分類
    DOCUMENT_CATEGORIES = {
        'technical': '技術類',
        'contract': '業務合約類', 
        'customer_service': '客服類',
        'others': '其他類'
    }
    
    # 向量檢索設定
    VECTOR_DB_PATH = os.path.join(os.getcwd(), 'vector_db')
    CHUNK_SIZE = 600  # 每個文檔片段的字數
    CHUNK_OVERLAP = 125  # 片段重疊字數
    
    # AI 模型設定
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    AZURE_OPENAI_ENDPOINT = os.environ.get('AZURE_OPENAI_ENDPOINT')
    AZURE_OPENAI_KEY = os.environ.get('AZURE_OPENAI_KEY')
    
    # 本地模型設定 (Ollama)
    OLLAMA_BASE_URL = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')
    OLLAMA_MODEL = os.environ.get('OLLAMA_MODEL', 'llama3')
    
    # 使用的AI服務 ('openai', 'azure', 'ollama')
    AI_SERVICE = os.environ.get('AI_SERVICE', 'openai')
    
    # OCR 設定
    TESSERACT_PATH = os.environ.get('TESSERACT_PATH', r'C:\Program Files\Tesseract-OCR\tesseract.exe')
    
    # 日誌設定
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.path.join(os.getcwd(), 'logs', 'app.log')

class DevelopmentConfig(Config):
    """開發環境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生產環境配置"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """測試環境配置"""
    DEBUG = True
    TESTING = True
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'test_uploads')
    VECTOR_DB_PATH = os.path.join(os.getcwd(), 'test_vector_db')

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """獲取當前配置"""
    return config[os.environ.get('FLASK_ENV', 'default')]
