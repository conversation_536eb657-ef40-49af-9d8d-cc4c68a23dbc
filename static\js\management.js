// 管理頁面 JavaScript

let currentPage = 1;
let currentCategory = 'all';
let deleteDocId = null;

$(document).ready(function() {
    loadStatistics();
    loadDocuments();
    loadAIStatus();
});

// 載入統計信息
function loadStatistics() {
    $.ajax({
        url: '/api/statistics',
        method: 'GET',
        success: function(data) {
            updateStatistics(data);
        },
        error: function(xhr, status, error) {
            console.error('載入統計信息失敗:', error);
            showAlert('載入統計信息失敗', 'danger');
        }
    });
}

// 更新統計顯示
function updateStatistics(data) {
    if (data.documents) {
        $('#totalDocs').text(data.documents.total_documents || 0);
        $('#totalSize').text(formatFileSize(data.documents.total_size || 0));
        
        // 更新分類統計
        updateCategoryStats(data.documents.category_names || {});
    }
    
    if (data.vector_database) {
        $('#totalChunks').text(data.vector_database.total_chunks || 0);
    }
}

// 更新分類統計
function updateCategoryStats(categories) {
    const container = $('#categoryStats');
    container.empty();
    
    for (const [categoryName, count] of Object.entries(categories)) {
        const html = `
            <div class="col-md-3 mb-2">
                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                    <span>${categoryName}</span>
                    <span class="badge bg-primary">${count}</span>
                </div>
            </div>
        `;
        container.append(html);
    }
}

// 載入文檔列表
function loadDocuments(page = 1, category = 'all') {
    currentPage = page;
    currentCategory = category;
    
    $.ajax({
        url: '/api/documents',
        method: 'GET',
        data: {
            page: page,
            per_page: 10,
            category: category
        },
        success: function(data) {
            updateDocumentsTable(data.documents);
            updatePagination(data.pagination);
        },
        error: function(xhr, status, error) {
            console.error('載入文檔列表失敗:', error);
            showAlert('載入文檔列表失敗', 'danger');
        }
    });
}

// 更新文檔表格
function updateDocumentsTable(documents) {
    const tbody = $('#documentsTable');
    tbody.empty();
    
    if (documents.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted">沒有找到文檔</td>
            </tr>
        `);
        return;
    }
    
    documents.forEach(doc => {
        const row = `
            <tr>
                <td>
                    <i class="fas fa-file-${getFileIcon(doc.file_name)} me-2"></i>
                    ${doc.file_name}
                </td>
                <td>
                    <span class="badge bg-secondary">${doc.category_name}</span>
                </td>
                <td>${formatFileSize(doc.file_size)}</td>
                <td>${formatDateTime(doc.processed_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="viewDocument('${doc.doc_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDocument('${doc.doc_id}', '${doc.file_name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 更新分頁
function updatePagination(pagination) {
    const container = $('#pagination');
    container.empty();
    
    if (pagination.pages <= 1) return;
    
    // 上一頁
    const prevDisabled = pagination.page <= 1 ? 'disabled' : '';
    container.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="loadDocuments(${pagination.page - 1}, '${currentCategory}')">上一頁</a>
        </li>
    `);
    
    // 頁碼
    for (let i = 1; i <= pagination.pages; i++) {
        const active = i === pagination.page ? 'active' : '';
        container.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadDocuments(${i}, '${currentCategory}')">${i}</a>
            </li>
        `);
    }
    
    // 下一頁
    const nextDisabled = pagination.page >= pagination.pages ? 'disabled' : '';
    container.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="loadDocuments(${pagination.page + 1}, '${currentCategory}')">下一頁</a>
        </li>
    `);
}

// 載入AI狀態
function loadAIStatus() {
    $.ajax({
        url: '/api/ai/status',
        method: 'GET',
        success: function(data) {
            updateAIStatus(data);
        },
        error: function(xhr, status, error) {
            console.error('載入AI狀態失敗:', error);
            $('#aiStatus').text('錯誤');
            $('#aiServiceInfo').html('<p class="text-danger">載入AI狀態失敗</p>');
        }
    });
}

// 更新AI狀態顯示
function updateAIStatus(data) {
    if (data.available) {
        $('#aiStatus').text('可用');
        
        const info = data.service_info;
        let statusHtml = `
            <div class="row">
                <div class="col-md-6">
                    <strong>服務類型:</strong> ${info.service}<br>
                    <strong>狀態:</strong> <span class="badge bg-${getStatusColor(info.status)}">${getStatusText(info.status)}</span>
                </div>
                <div class="col-md-6">
        `;
        
        if (info.model) {
            statusHtml += `<strong>模型:</strong> ${info.model}<br>`;
        }
        if (info.endpoint) {
            statusHtml += `<strong>端點:</strong> ${info.endpoint}<br>`;
        }
        if (info.base_url) {
            statusHtml += `<strong>URL:</strong> ${info.base_url}<br>`;
        }
        
        statusHtml += `
                </div>
            </div>
        `;
        
        $('#aiServiceInfo').html(statusHtml);
    } else {
        $('#aiStatus').text('不可用');
        $('#aiServiceInfo').html('<p class="text-warning">AI服務未配置或不可用</p>');
    }
}

// 獲取狀態顏色
function getStatusColor(status) {
    const colors = {
        'connected': 'success',
        'configured': 'info',
        'disconnected': 'danger',
        'not_configured': 'warning'
    };
    return colors[status] || 'secondary';
}

// 獲取狀態文字
function getStatusText(status) {
    const texts = {
        'connected': '已連接',
        'configured': '已配置',
        'disconnected': '連接失敗',
        'not_configured': '未配置'
    };
    return texts[status] || status;
}

// 獲取文件圖標
function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    return ext === 'pdf' ? 'pdf' : 'word';
}

// 格式化日期時間
function formatDateTime(isoString) {
    const date = new Date(isoString);
    return date.toLocaleString('zh-TW');
}

// 過濾文檔
function filterDocuments() {
    const category = $('#categoryFilter').val();
    loadDocuments(1, category);
}

// 刷新統計
function refreshStats() {
    showLoadingModal('正在刷新統計信息...');
    loadStatistics();
    loadDocuments(currentPage, currentCategory);
    hideLoadingModal();
}

// 查看文檔詳情
function viewDocument(docId) {
    $.ajax({
        url: `/api/documents/${docId}`,
        method: 'GET',
        success: function(data) {
            // 顯示文檔詳情（可以創建一個模態框）
            alert(`文檔詳情:\n文件名: ${data.file_name}\n分類: ${data.category_name}\n大小: ${formatFileSize(data.file_size)}\n文字長度: ${data.text_length}`);
        },
        error: function(xhr, status, error) {
            showAlert('載入文檔詳情失敗', 'danger');
        }
    });
}

// 刪除文檔
function deleteDocument(docId, fileName) {
    deleteDocId = docId;
    $('#deleteFileName').text(fileName);
    $('#deleteModal').modal('show');
}

// 確認刪除
function confirmDelete() {
    if (!deleteDocId) return;
    
    $.ajax({
        url: `/api/documents/${deleteDocId}`,
        method: 'DELETE',
        success: function(data) {
            showAlert('文檔刪除成功', 'success');
            $('#deleteModal').modal('hide');
            loadDocuments(currentPage, currentCategory);
            loadStatistics();
            deleteDocId = null;
        },
        error: function(xhr, status, error) {
            showAlert('刪除文檔失敗', 'danger');
        }
    });
}

// 重建索引
function rebuildIndex() {
    $('#rebuildModal').modal('show');
}

// 確認重建索引
function confirmRebuild() {
    $('#rebuildModal').modal('hide');
    showLoadingModal('正在重建索引，請稍候...');
    
    $.ajax({
        url: '/api/rebuild-index',
        method: 'POST',
        success: function(data) {
            hideLoadingModal();
            showAlert(`索引重建完成，成功: ${data.success_count}，失敗: ${data.error_count}`, 'success');
            loadStatistics();
            loadDocuments(currentPage, currentCategory);
        },
        error: function(xhr, status, error) {
            hideLoadingModal();
            showAlert('重建索引失敗', 'danger');
        }
    });
}

// 測試AI連接
function testAI() {
    showLoadingModal('正在測試AI連接...');
    
    $.ajax({
        url: '/api/ai/test',
        method: 'GET',
        success: function(data) {
            hideLoadingModal();
            if (data.success) {
                showAlert('AI連接測試成功', 'success');
            } else {
                showAlert(`AI連接測試失敗: ${data.message}`, 'danger');
            }
        },
        error: function(xhr, status, error) {
            hideLoadingModal();
            showAlert('AI連接測試失敗', 'danger');
        }
    });
}
