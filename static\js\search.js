// 搜索功能 JavaScript

$(document).ready(function() {
    // 搜索表單提交
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        performSearch();
    });
    
    // 搜索建議功能
    $('#queryInput').on('input', debounce(function() {
        const query = $(this).val().trim();
        if (query.length >= 2) {
            getSuggestions(query);
        }
    }, 300));
    
    // 回車鍵搜索
    $('#queryInput').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            performSearch();
        }
    });
});

// 執行搜索
function performSearch() {
    const query = $('#queryInput').val().trim();
    const category = $('#categorySelect').val();
    
    // 驗證輸入
    if (!query) {
        showAlert('請輸入查詢問題', 'warning');
        $('#queryInput').focus();
        return;
    }
    
    // 顯示載入狀態
    showLoadingModal('正在搜索相關文檔...');
    $('#searchBtn').prop('disabled', true).html('<span class="loading-spinner"></span> 搜索中...');
    
    // 發送搜索請求
    $.ajax({
        url: '/api/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: query,
            category: category
        }),
        success: function(response) {
            displaySearchResults(response);
            hideLoadingModal();
        },
        error: function(xhr, status, error) {
            console.error('搜索錯誤:', error);
            let errorMessage = '搜索失敗，請稍後再試';
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            
            showAlert(errorMessage, 'danger');
            hideLoadingModal();
        },
        complete: function() {
            $('#searchBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>搜索');
        }
    });
}

// 顯示搜索結果
function displaySearchResults(response) {
    const resultsContainer = $('#searchResults');
    const answerContainer = $('#answerContent');
    const sourcesContainer = $('#sourcesContent');
    
    // 顯示AI回答
    answerContainer.html(`
        <div class="search-result-item">
            <h6 class="text-primary mb-2">
                <i class="fas fa-robot me-2"></i>AI 智能回答
            </h6>
            <p class="mb-0">${formatAnswer(response.answer)}</p>
        </div>
    `);
    
    // 顯示來源文檔
    if (response.sources && response.sources.length > 0) {
        let sourcesHtml = '';
        response.sources.forEach((source, index) => {
            sourcesHtml += createSourceItem(source, index + 1);
        });
        sourcesContainer.html(sourcesHtml);
        
        // 綁定來源點擊事件
        $('.source-item').on('click', function() {
            const filePath = $(this).data('file-path');
            copyToClipboard(filePath);
        });
    } else {
        sourcesContainer.html('<p class="text-muted">未找到相關文檔</p>');
    }
    
    // 顯示結果區域
    resultsContainer.show();
    
    // 滾動到結果區域
    $('html, body').animate({
        scrollTop: resultsContainer.offset().top - 80
    }, 500);
}

// 格式化AI回答（處理引用標記）
function formatAnswer(answer) {
    // 將 [1], [2] 等引用標記轉換為可點擊的鏈接
    return answer.replace(/\[(\d+)\]/g, '<a href="#source-$1" class="citation">[$1]</a>');
}

// 創建來源項目HTML
function createSourceItem(source, index) {
    const relevancePercent = Math.round(source.relevance_score * 100);
    const categoryName = getCategoryName(source.category);

    return `
        <div class="source-item" data-file-path="${source.file_path}" id="source-${index}">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">
                        <span class="badge bg-secondary me-2">[${index}]</span>
                        <i class="fas fa-file-${getFileIcon(source.file_name)} me-1"></i>
                        ${source.file_name}
                    </h6>
                    <p class="mb-1 text-muted small">
                        <i class="fas fa-folder me-1"></i>
                        分類：${categoryName} |
                        <i class="fas fa-chart-line me-1"></i>
                        相關度：${relevancePercent}%
                    </p>
                    ${source.excerpt ? `<p class="mb-0 small">${highlightKeywords(source.excerpt, getCurrentQuery())}</p>` : ''}
                </div>
                <div class="btn-group-vertical btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="copyToClipboard('${source.file_path}')" title="複製路徑">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewDocumentDetail('${source.doc_id}')" title="查看詳情">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 獲取文件圖標
function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    return ext === 'pdf' ? 'pdf' : 'word';
}

// 高亮關鍵詞
function highlightKeywords(text, query) {
    if (!query) return text;

    const keywords = query.split(/\s+/).filter(k => k.length > 1);
    let highlightedText = text;

    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });

    return highlightedText;
}

// 獲取當前查詢
function getCurrentQuery() {
    return $('#queryInput').val().trim();
}

// 查看文檔詳情
function viewDocumentDetail(docId) {
    if (!docId) return;

    $.ajax({
        url: `/api/documents/${docId}`,
        method: 'GET',
        success: function(data) {
            showDocumentModal(data);
        },
        error: function(xhr, status, error) {
            showAlert('載入文檔詳情失敗', 'danger');
        }
    });
}

// 顯示文檔詳情模態框
function showDocumentModal(document) {
    const modalHtml = `
        <div class="modal fade" id="documentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-${getFileIcon(document.file_name)} me-2"></i>
                            ${document.file_name}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>分類:</strong> ${document.category_name}<br>
                                <strong>檔案大小:</strong> ${formatFileSize(document.file_size)}<br>
                                <strong>頁數/段落:</strong> ${document.page_count}<br>
                            </div>
                            <div class="col-md-6">
                                <strong>文字長度:</strong> ${document.text_length.toLocaleString()} 字<br>
                                <strong>處理時間:</strong> ${new Date(document.processed_at).toLocaleString('zh-TW')}<br>
                                <strong>文檔ID:</strong> <code>${document.doc_id}</code>
                            </div>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-primary" onclick="copyToClipboard('${document.file_path}')">
                                <i class="fas fa-copy me-1"></i>複製路徑
                            </button>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard('${document.doc_id}')">
                                <i class="fas fa-fingerprint me-1"></i>複製ID
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除現有模態框
    $('#documentModal').remove();

    // 添加新模態框
    $('body').append(modalHtml);

    // 顯示模態框
    $('#documentModal').modal('show');

    // 模態框關閉時移除
    $('#documentModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

// 獲取分類名稱
function getCategoryName(category) {
    const categories = {
        'technical': '技術類',
        'contract': '業務合約類',
        'customer_service': '客服類',
        'others': '其他類'
    };
    return categories[category] || category;
}

// 獲取搜索建議
function getSuggestions(query) {
    $.ajax({
        url: '/api/search/suggestions',
        method: 'GET',
        data: { q: query },
        success: function(response) {
            if (response.suggestions && response.suggestions.length > 0) {
                displaySuggestions(response.suggestions);
            }
        },
        error: function(xhr, status, error) {
            console.error('獲取建議失敗:', error);
        }
    });
}

// 顯示搜索建議
function displaySuggestions(suggestions) {
    // 移除現有的建議列表
    $('.suggestions-dropdown').remove();
    
    if (suggestions.length === 0) return;
    
    let suggestionsHtml = '<div class="suggestions-dropdown list-group position-absolute w-100" style="z-index: 1000;">';
    suggestions.forEach(suggestion => {
        suggestionsHtml += `
            <button type="button" class="list-group-item list-group-item-action suggestion-item">
                <i class="fas fa-search me-2 text-muted"></i>
                ${suggestion}
            </button>
        `;
    });
    suggestionsHtml += '</div>';
    
    // 添加建議列表
    $('#queryInput').parent().append(suggestionsHtml);
    
    // 綁定建議點擊事件
    $('.suggestion-item').on('click', function() {
        const suggestion = $(this).text().trim();
        $('#queryInput').val(suggestion);
        $('.suggestions-dropdown').remove();
        performSearch();
    });
    
    // 點擊其他地方隱藏建議
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#queryInput').length) {
            $('.suggestions-dropdown').remove();
        }
    });
}
