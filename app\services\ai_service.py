"""
AI問答服務
支援OpenAI、Azure OpenAI和本地Ollama模型
"""

import json
import requests
from typing import List, Dict, Optional
from loguru import logger


class AIService:
    """AI問答服務"""
    
    def __init__(self, config):
        self.config = config
        self.ai_service = config.get('AI_SERVICE', 'openai')
        
        # 根據配置初始化對應的AI服務
        if self.ai_service == 'openai':
            self._init_openai()
        elif self.ai_service == 'azure':
            self._init_azure_openai()
        elif self.ai_service == 'ollama':
            self._init_ollama()
        else:
            raise ValueError(f"不支援的AI服務: {self.ai_service}")
        
        logger.info(f"AI服務初始化完成: {self.ai_service}")
    
    def _init_openai(self):
        """初始化OpenAI服務"""
        self.api_key = self.config.get('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY未配置")
        
        # 導入OpenAI庫
        try:
            import openai
            openai.api_key = self.api_key
            self.openai = openai
        except ImportError:
            raise ImportError("請安裝openai庫: pip install openai")
    
    def _init_azure_openai(self):
        """初始化Azure OpenAI服務"""
        self.azure_endpoint = self.config.get('AZURE_OPENAI_ENDPOINT')
        self.azure_key = self.config.get('AZURE_OPENAI_KEY')
        
        if not self.azure_endpoint or not self.azure_key:
            raise ValueError("Azure OpenAI配置不完整")
        
        # 導入OpenAI庫並配置Azure
        try:
            import openai
            openai.api_type = "azure"
            openai.api_base = self.azure_endpoint
            openai.api_key = self.azure_key
            openai.api_version = "2023-05-15"
            self.openai = openai
        except ImportError:
            raise ImportError("請安裝openai庫: pip install openai")
    
    def _init_ollama(self):
        """初始化Ollama本地服務"""
        self.ollama_base_url = self.config.get('OLLAMA_BASE_URL', 'http://localhost:11434')
        self.ollama_model = self.config.get('OLLAMA_MODEL', 'llama3')
        
        # 測試Ollama連接
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                raise ConnectionError("無法連接到Ollama服務")
        except requests.RequestException as e:
            raise ConnectionError(f"Ollama服務連接失敗: {e}")
    
    def generate_answer(self, query: str, context_documents: List[Dict], 
                       category: str = "all") -> Dict:
        """
        生成AI答案
        
        Args:
            query: 用戶查詢
            context_documents: 相關文檔列表
            category: 查詢分類
            
        Returns:
            AI回答結果
        """
        try:
            # 構建上下文
            context = self._build_context(context_documents)
            
            # 構建提示詞
            prompt = self._build_prompt(query, context, category)
            
            # 根據AI服務類型生成回答
            if self.ai_service in ['openai', 'azure']:
                return self._generate_openai_answer(prompt)
            elif self.ai_service == 'ollama':
                return self._generate_ollama_answer(prompt)
            else:
                raise ValueError(f"不支援的AI服務: {self.ai_service}")
                
        except Exception as e:
            logger.error(f"生成AI答案失敗: {e}")
            return {
                'success': False,
                'answer': '抱歉，AI服務暫時無法使用，請稍後再試。',
                'error': str(e)
            }
    
    def _build_context(self, documents: List[Dict]) -> str:
        """構建上下文文本"""
        if not documents:
            return "沒有找到相關文檔。"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(
                f"[文檔{i}] {doc['file_name']}\n"
                f"分類: {doc['category']}\n"
                f"內容: {doc['excerpt']}\n"
            )
        
        return "\n".join(context_parts)
    
    def _build_prompt(self, query: str, context: str, category: str) -> str:
        """構建提示詞"""
        category_names = {
            'technical': '技術類',
            'contract': '業務合約類',
            'customer_service': '客服類',
            'others': '其他類',
            'all': '所有分類'
        }
        
        category_name = category_names.get(category, category)
        
        prompt = f"""你是一個專業的企業知識管理助手。請根據提供的文檔內容回答用戶的問題。

查詢分類: {category_name}
用戶問題: {query}

相關文檔:
{context}

請根據上述文檔內容回答用戶的問題。要求：
1. 回答要準確、專業、有條理
2. 如果文檔中沒有相關信息，請明確說明
3. 在回答中標註引用的文檔編號，如[文檔1]、[文檔2]
4. 回答要簡潔明瞭，避免冗長
5. 使用繁體中文回答

回答:"""
        
        return prompt
    
    def _generate_openai_answer(self, prompt: str) -> Dict:
        """使用OpenAI生成答案"""
        try:
            response = self.openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一個專業的企業知識管理助手。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            answer = response.choices[0].message.content.strip()
            
            return {
                'success': True,
                'answer': answer,
                'model': 'gpt-3.5-turbo',
                'tokens_used': response.usage.total_tokens
            }
            
        except Exception as e:
            logger.error(f"OpenAI API調用失敗: {e}")
            return {
                'success': False,
                'answer': '抱歉，AI服務暫時無法使用。',
                'error': str(e)
            }
    
    def _generate_ollama_answer(self, prompt: str) -> Dict:
        """使用Ollama生成答案"""
        try:
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.ollama_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "top_p": 0.9,
                        "max_tokens": 1000
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('response', '').strip()
                
                return {
                    'success': True,
                    'answer': answer,
                    'model': self.ollama_model,
                    'tokens_used': len(prompt.split()) + len(answer.split())
                }
            else:
                raise Exception(f"Ollama API錯誤: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Ollama API調用失敗: {e}")
            return {
                'success': False,
                'answer': '抱歉，本地AI服務暫時無法使用。',
                'error': str(e)
            }
    
    def test_connection(self) -> Dict:
        """測試AI服務連接"""
        try:
            test_query = "測試連接"
            test_context = [{'file_name': 'test.pdf', 'category': 'technical', 'excerpt': '這是測試內容'}]
            
            result = self.generate_answer(test_query, test_context)
            
            return {
                'success': result['success'],
                'service': self.ai_service,
                'message': '連接正常' if result['success'] else '連接失敗',
                'error': result.get('error')
            }
            
        except Exception as e:
            logger.error(f"AI服務連接測試失敗: {e}")
            return {
                'success': False,
                'service': self.ai_service,
                'message': '連接測試失敗',
                'error': str(e)
            }
    
    def get_service_info(self) -> Dict:
        """獲取AI服務信息"""
        info = {
            'service': self.ai_service,
            'status': 'unknown'
        }
        
        if self.ai_service == 'openai':
            info['model'] = 'gpt-3.5-turbo'
            info['status'] = 'configured' if self.api_key else 'not_configured'
        elif self.ai_service == 'azure':
            info['endpoint'] = self.azure_endpoint
            info['status'] = 'configured' if (self.azure_endpoint and self.azure_key) else 'not_configured'
        elif self.ai_service == 'ollama':
            info['base_url'] = self.ollama_base_url
            info['model'] = self.ollama_model
            try:
                response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
                info['status'] = 'connected' if response.status_code == 200 else 'disconnected'
            except:
                info['status'] = 'disconnected'
        
        return info
