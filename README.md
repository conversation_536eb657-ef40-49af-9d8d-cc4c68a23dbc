# 公司知識管理系統

一個基於Flask的企業級知識管理系統，支援PDF和Word文檔的智能檢索和問答。

## 功能特色

- 📁 **文檔分類管理**: 支援技術類、業務合約類、客服類、其他類四大分類
- 📤 **智能文檔上傳**: 支援PDF和DOCX格式，自動MD5重複檢測
- 🔍 **向量檢索**: 基於Sentence Transformers的語義檢索
- 🤖 **AI問答**: 整合OpenAI/Azure/Ollama提供智能問答
- 📱 **簡潔介面**: Bootstrap響應式設計，操作簡單
- 🔒 **本地部署**: 支援Windows Server內網部署

## 系統架構

```
knowledge-management/
├── app/                    # 應用核心
│   ├── models/            # 數據模型
│   ├── services/          # 業務邏輯
│   ├── utils/             # 工具函數
│   └── routes/            # 路由處理
├── static/                # 靜態資源
├── templates/             # HTML模板
├── uploads/               # 文檔存儲
│   ├── technical/         # 技術類文檔
│   ├── contract/          # 業務合約類
│   ├── customer_service/  # 客服類
│   └── others/            # 其他類
├── logs/                  # 日誌文件
└── vector_db/             # 向量數據庫
```

## 快速開始

### 1. 環境準備

```bash
# 安裝Python依賴
pip install -r requirements.txt

# 複製環境配置文件
copy .env.example .env
```

### 2. 配置設定

編輯 `.env` 文件，配置AI服務：

```env
# 選擇AI服務 (openai/azure/ollama)
AI_SERVICE=openai

# OpenAI配置
OPENAI_API_KEY=your-api-key

# 或Azure OpenAI配置
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_KEY=your-azure-key

# 或本地Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3
```

### 3. 啟動系統

```bash
# 開發模式
python app.py

# 生產模式
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### 4. 訪問系統

開啟瀏覽器訪問: http://localhost:5000

## 部署說明

### Windows Server 部署

1. **安裝Python 3.8+**
2. **安裝Tesseract OCR** (可選，用於掃描PDF)
3. **配置IIS** (可選，使用FastCGI)
4. **設定防火牆規則**

### 生產環境配置

```bash
# 設定環境變數
set FLASK_ENV=production

# 使用Waitress運行
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

## API文檔

### 文檔上傳
```
POST /api/upload
Content-Type: multipart/form-data

Parameters:
- file: 文檔文件 (PDF/DOCX)
- category: 分類 (technical/contract/customer_service/others)
```

### 智能檢索
```
POST /api/search
Content-Type: application/json

{
  "query": "查詢問題",
  "category": "all" | "technical" | "contract" | "customer_service" | "others"
}
```

## 技術棧

- **後端**: Flask, Python 3.8+
- **向量檢索**: ChromaDB, Sentence Transformers
- **文檔處理**: PyPDF2, python-docx, Tesseract OCR
- **AI服務**: OpenAI API, Azure OpenAI, Ollama
- **前端**: Bootstrap 5, jQuery
- **部署**: Waitress, IIS (可選)

## 開發指南

### 添加新功能

1. 在 `app/services/` 中實現業務邏輯
2. 在 `app/routes/` 中添加API端點
3. 在 `templates/` 中更新前端界面
4. 編寫測試用例

### 測試

```bash
# 運行測試
pytest tests/

# 代碼格式化
black app/
flake8 app/
```

## 常見問題

### Q: 如何配置OCR功能？
A: 下載安裝Tesseract OCR，並在.env中設定TESSERACT_PATH

### Q: 支援哪些文檔格式？
A: 目前支援PDF和DOCX格式，不支援舊版DOC格式

### Q: 如何切換AI服務？
A: 修改.env中的AI_SERVICE參數，可選openai/azure/ollama

## 授權

本項目為企業內部使用，請遵守相關授權協議。
