"""
文件處理工具函數
"""

import os
import hashlib
import shutil
from pathlib import Path
from typing import Op<PERSON>, Tuple
from werkzeug.utils import secure_filename
from loguru import logger


def calculate_file_md5(file_path: str) -> str:
    """
    計算文件的MD5值
    
    Args:
        file_path: 文件路徑
        
    Returns:
        MD5哈希值
    """
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"計算MD5失敗 {file_path}: {e}")
        raise


def get_safe_filename(filename: str) -> str:
    """
    獲取安全的文件名
    
    Args:
        filename: 原始文件名
        
    Returns:
        安全的文件名
    """
    return secure_filename(filename)


def ensure_directory_exists(directory_path: str) -> bool:
    """
    確保目錄存在
    
    Args:
        directory_path: 目錄路徑
        
    Returns:
        是否成功創建或已存在
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"創建目錄失敗 {directory_path}: {e}")
        return False


def get_file_info(file_path: str) -> dict:
    """
    獲取文件基本信息
    
    Args:
        file_path: 文件路徑
        
    Returns:
        文件信息字典
    """
    try:
        file_stat = os.stat(file_path)
        file_path_obj = Path(file_path)
        
        return {
            'name': file_path_obj.name,
            'size': file_stat.st_size,
            'extension': file_path_obj.suffix.lower(),
            'created_time': file_stat.st_ctime,
            'modified_time': file_stat.st_mtime,
            'absolute_path': str(file_path_obj.absolute())
        }
    except Exception as e:
        logger.error(f"獲取文件信息失敗 {file_path}: {e}")
        raise


def is_valid_file_type(filename: str, allowed_extensions: set) -> bool:
    """
    檢查文件類型是否有效
    
    Args:
        filename: 文件名
        allowed_extensions: 允許的擴展名集合
        
    Returns:
        是否為有效類型
    """
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in allowed_extensions


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 文件大小（字節）
        
    Returns:
        格式化的大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def move_file(source_path: str, destination_path: str) -> bool:
    """
    移動文件
    
    Args:
        source_path: 源文件路徑
        destination_path: 目標文件路徑
        
    Returns:
        是否移動成功
    """
    try:
        # 確保目標目錄存在
        destination_dir = os.path.dirname(destination_path)
        ensure_directory_exists(destination_dir)
        
        # 移動文件
        shutil.move(source_path, destination_path)
        logger.info(f"文件移動成功: {source_path} -> {destination_path}")
        return True
    except Exception as e:
        logger.error(f"文件移動失敗: {e}")
        return False


def copy_file(source_path: str, destination_path: str) -> bool:
    """
    複製文件
    
    Args:
        source_path: 源文件路徑
        destination_path: 目標文件路徑
        
    Returns:
        是否複製成功
    """
    try:
        # 確保目標目錄存在
        destination_dir = os.path.dirname(destination_path)
        ensure_directory_exists(destination_dir)
        
        # 複製文件
        shutil.copy2(source_path, destination_path)
        logger.info(f"文件複製成功: {source_path} -> {destination_path}")
        return True
    except Exception as e:
        logger.error(f"文件複製失敗: {e}")
        return False


def delete_file(file_path: str) -> bool:
    """
    刪除文件
    
    Args:
        file_path: 文件路徑
        
    Returns:
        是否刪除成功
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"文件刪除成功: {file_path}")
            return True
        else:
            logger.warning(f"文件不存在: {file_path}")
            return False
    except Exception as e:
        logger.error(f"文件刪除失敗: {e}")
        return False


def find_duplicate_files(directory: str) -> dict:
    """
    查找重複文件
    
    Args:
        directory: 搜索目錄
        
    Returns:
        重複文件字典 {md5: [file_paths]}
    """
    md5_dict = {}
    duplicates = {}
    
    try:
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_md5 = calculate_file_md5(file_path)
                    
                    if file_md5 in md5_dict:
                        if file_md5 not in duplicates:
                            duplicates[file_md5] = [md5_dict[file_md5]]
                        duplicates[file_md5].append(file_path)
                    else:
                        md5_dict[file_md5] = file_path
                        
                except Exception as e:
                    logger.warning(f"處理文件失敗 {file_path}: {e}")
                    continue
        
        logger.info(f"找到 {len(duplicates)} 組重複文件")
        return duplicates
        
    except Exception as e:
        logger.error(f"查找重複文件失敗: {e}")
        return {}


def cleanup_empty_directories(directory: str) -> int:
    """
    清理空目錄
    
    Args:
        directory: 要清理的目錄
        
    Returns:
        清理的目錄數量
    """
    cleaned_count = 0
    
    try:
        for root, dirs, files in os.walk(directory, topdown=False):
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):  # 目錄為空
                        os.rmdir(dir_path)
                        logger.info(f"清理空目錄: {dir_path}")
                        cleaned_count += 1
                except Exception as e:
                    logger.warning(f"清理目錄失敗 {dir_path}: {e}")
        
        logger.info(f"共清理了 {cleaned_count} 個空目錄")
        return cleaned_count
        
    except Exception as e:
        logger.error(f"清理空目錄失敗: {e}")
        return 0


def get_directory_size(directory: str) -> int:
    """
    獲取目錄總大小
    
    Args:
        directory: 目錄路徑
        
    Returns:
        目錄大小（字節）
    """
    total_size = 0
    
    try:
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except Exception as e:
                    logger.warning(f"獲取文件大小失敗 {file_path}: {e}")
        
        return total_size
        
    except Exception as e:
        logger.error(f"獲取目錄大小失敗: {e}")
        return 0
