@echo off
echo ========================================
echo 公司知識管理系統 - 安裝腳本
echo ========================================

echo.
echo 1. 檢查Python版本...
python --version
if %errorlevel% neq 0 (
    echo 錯誤: 未找到Python，請先安裝Python 3.8或更高版本
    pause
    exit /b 1
)

echo.
echo 2. 升級pip...
python -m pip install --upgrade pip

echo.
echo 3. 安裝Python依賴...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 錯誤: 依賴安裝失敗
    pause
    exit /b 1
)

echo.
echo 4. 創建環境配置文件...
if not exist .env (
    copy .env.example .env
    echo 已創建 .env 文件，請編輯配置您的設定
) else (
    echo .env 文件已存在
)

echo.
echo 5. 創建必要目錄...
if not exist uploads mkdir uploads
if not exist uploads\technical mkdir uploads\technical
if not exist uploads\contract mkdir uploads\contract
if not exist uploads\customer_service mkdir uploads\customer_service
if not exist uploads\others mkdir uploads\others
if not exist logs mkdir logs
if not exist vector_db mkdir vector_db

echo.
echo ========================================
echo 安裝完成！
echo ========================================
echo.
echo 下一步：
echo 1. 編輯 .env 文件配置您的設定
echo 2. 運行 python app.py 啟動開發服務器
echo 3. 或運行 python run_production.py 啟動生產服務器
echo.
pause
