from flask import Blueprint, request, jsonify, current_app
from loguru import logger

from app.services.indexing_service import IndexingService
from app.services.ai_service import AIService

search_bp = Blueprint('search', __name__)

# 全局服務實例
_indexing_service = None
_ai_service = None

def get_indexing_service():
    """獲取索引服務實例"""
    global _indexing_service
    if _indexing_service is None:
        _indexing_service = IndexingService(current_app.config)
    return _indexing_service

def get_ai_service():
    """獲取AI服務實例"""
    global _ai_service
    if _ai_service is None:
        try:
            _ai_service = AIService(current_app.config)
        except Exception as e:
            logger.error(f"AI服務初始化失敗: {e}")
            _ai_service = None
    return _ai_service

@search_bp.route('/search', methods=['POST'])
def search_documents():
    """文檔檢索端點"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '請求數據格式錯誤'}), 400

        query = data.get('query', '').strip()
        category = data.get('category', 'all')

        # 檢查查詢內容
        if not query:
            return jsonify({'error': '請輸入查詢問題'}), 400

        # 檢查分類
        valid_categories = list(current_app.config['DOCUMENT_CATEGORIES'].keys()) + ['all']
        if category not in valid_categories:
            return jsonify({'error': '無效的分類選擇'}), 400

        logger.info(f"Search request: query='{query}', category='{category}'")

        # 執行檢索
        indexing_service = get_indexing_service()
        search_result = indexing_service.search_documents(query, category, top_k=5)

        if not search_result['success']:
            return jsonify({'error': search_result['message']}), 500

        sources = search_result['results']

        # 嘗試使用AI生成答案
        ai_service = get_ai_service()
        if ai_service and sources:
            try:
                ai_result = ai_service.generate_answer(query, sources, category)
                if ai_result['success']:
                    answer = ai_result['answer']
                else:
                    # AI失敗時使用簡單摘要
                    answer = f"根據查詢「{query}」，找到 {len(sources)} 個相關文檔。"
                    if category != 'all':
                        category_name = current_app.config['DOCUMENT_CATEGORIES'].get(category, category)
                        answer += f" 搜索範圍：{category_name}。"
            except Exception as e:
                logger.error(f"AI生成答案失敗: {e}")
                answer = f"根據查詢「{query}」，找到 {len(sources)} 個相關文檔。"
        else:
            # 沒有AI服務或沒有找到文檔時的回應
            if sources:
                answer = f"根據查詢「{query}」，找到 {len(sources)} 個相關文檔。"
                if category != 'all':
                    category_name = current_app.config['DOCUMENT_CATEGORIES'].get(category, category)
                    answer += f" 搜索範圍：{category_name}。"
            else:
                answer = f"抱歉，沒有找到與「{query}」相關的文檔。"

        return jsonify({
            'answer': answer,
            'sources': sources,
            'query': query,
            'category': category,
            'total_sources': len(sources),
            'ai_enabled': ai_service is not None
        }), 200

    except Exception as e:
        logger.error(f"Error in search: {e}")
        return jsonify({'error': '檢索失敗，請稍後再試'}), 500

@search_bp.route('/search/suggestions')
def search_suggestions():
    """獲取搜索建議"""
    try:
        query = request.args.get('q', '').strip()

        if len(query) < 2:
            return jsonify({'suggestions': []})

        # TODO: 實現基於歷史查詢的建議功能

        # 臨時建議
        suggestions = [
            f"{query}相關技術文檔",
            f"{query}操作指南",
            f"{query}常見問題"
        ]

        return jsonify({'suggestions': suggestions[:5]})

    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        return jsonify({'suggestions': []})

@search_bp.route('/ai/status')
def ai_status():
    """獲取AI服務狀態"""
    try:
        ai_service = get_ai_service()

        if ai_service:
            service_info = ai_service.get_service_info()
            return jsonify({
                'available': True,
                'service_info': service_info
            }), 200
        else:
            return jsonify({
                'available': False,
                'message': 'AI服務未配置或初始化失敗'
            }), 200

    except Exception as e:
        logger.error(f"獲取AI狀態失敗: {e}")
        return jsonify({
            'available': False,
            'error': str(e)
        }), 500

@search_bp.route('/ai/test')
def test_ai():
    """測試AI服務連接"""
    try:
        ai_service = get_ai_service()

        if not ai_service:
            return jsonify({
                'success': False,
                'message': 'AI服務未配置'
            }), 400

        test_result = ai_service.test_connection()
        return jsonify(test_result), 200

    except Exception as e:
        logger.error(f"AI服務測試失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
