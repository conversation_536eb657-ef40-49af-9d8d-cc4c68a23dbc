"""
基本功能測試
"""

import os
import sys
import pytest
import tempfile
from pathlib import Path

# 添加項目根目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app import create_app
from app.services.document_processor import DocumentProcessor
from app.services.vector_search import VectorSearchService
from app.models.document import Document, DocumentManager


class TestBasicFunctionality:
    """基本功能測試類"""
    
    @pytest.fixture
    def app(self):
        """創建測試應用"""
        app = create_app()
        app.config['TESTING'] = True
        app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
        app.config['VECTOR_DB_PATH'] = tempfile.mkdtemp()
        return app
    
    @pytest.fixture
    def client(self, app):
        """創建測試客戶端"""
        return app.test_client()
    
    def test_app_creation(self, app):
        """測試應用創建"""
        assert app is not None
        assert app.config['TESTING'] is True
    
    def test_health_endpoint(self, client):
        """測試健康檢查端點"""
        response = client.get('/health')
        assert response.status_code == 200
        data = response.get_json()
        assert data['status'] == 'healthy'
    
    def test_config_endpoint(self, client):
        """測試配置端點"""
        response = client.get('/config')
        assert response.status_code == 200
        data = response.get_json()
        assert 'categories' in data
        assert 'max_file_size' in data
    
    def test_document_processor(self, app):
        """測試文檔處理器"""
        with app.app_context():
            processor = DocumentProcessor(app.config)
            assert processor is not None
            
            # 測試文字分割
            text = "這是一個測試文檔。" * 100
            chunks = processor.split_text_into_chunks(text, chunk_size=50, overlap=10)
            assert len(chunks) > 1
            assert all('text' in chunk for chunk in chunks)
    
    def test_document_model(self, app):
        """測試文檔模型"""
        with app.app_context():
            doc = Document(
                file_path="/test/path.pdf",
                file_name="test.pdf",
                file_size=1024,
                file_md5="test_md5",
                category="technical",
                text_content="測試內容"
            )
            
            assert doc.file_name == "test.pdf"
            assert doc.category == "technical"
            
            # 測試序列化
            doc_dict = doc.to_dict()
            assert doc_dict['file_name'] == "test.pdf"
            
            # 測試反序列化
            doc2 = Document.from_dict(doc_dict)
            assert doc2.file_name == doc.file_name
    
    def test_document_manager(self, app):
        """測試文檔管理器"""
        with app.app_context():
            manager = DocumentManager(app.config['UPLOAD_FOLDER'])
            assert manager is not None
            
            # 測試添加文檔
            doc = Document(
                file_path="/test/path.pdf",
                file_name="test.pdf",
                file_size=1024,
                file_md5="test_md5_unique",
                category="technical",
                text_content="測試內容"
            )
            
            success = manager.add_document(doc)
            assert success is True
            
            # 測試獲取文檔
            retrieved_doc = manager.get_document(doc.doc_id)
            assert retrieved_doc is not None
            assert retrieved_doc.file_name == "test.pdf"
            
            # 測試重複添加
            success2 = manager.add_document(doc)
            assert success2 is False
    
    def test_search_endpoints(self, client):
        """測試搜索端點"""
        # 測試搜索建議
        response = client.get('/api/search/suggestions?q=test')
        assert response.status_code == 200
        data = response.get_json()
        assert 'suggestions' in data
        
        # 測試AI狀態
        response = client.get('/api/ai/status')
        assert response.status_code == 200
        data = response.get_json()
        assert 'available' in data
    
    def test_management_endpoints(self, client):
        """測試管理端點"""
        # 測試統計信息
        response = client.get('/api/statistics')
        assert response.status_code == 200
        data = response.get_json()
        assert 'documents' in data or 'system_status' in data
        
        # 測試分類列表
        response = client.get('/api/categories')
        assert response.status_code == 200
        data = response.get_json()
        assert 'categories' in data
        
        # 測試健康檢查
        response = client.get('/api/health')
        assert response.status_code == 200
        data = response.get_json()
        assert 'status' in data


def test_file_operations():
    """測試文件操作"""
    from app.utils.file_utils import calculate_file_md5, format_file_size, is_valid_file_type
    
    # 測試文件大小格式化
    assert format_file_size(1024) == "1.0 KB"
    assert format_file_size(1024 * 1024) == "1.0 MB"
    
    # 測試文件類型驗證
    assert is_valid_file_type("test.pdf", {'pdf', 'docx'}) is True
    assert is_valid_file_type("test.txt", {'pdf', 'docx'}) is False
    assert is_valid_file_type("test", {'pdf', 'docx'}) is False


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
