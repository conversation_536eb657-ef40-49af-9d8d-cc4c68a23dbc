{% extends "base.html" %}

{% block title %}系統管理 - 公司知識管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-cogs me-2"></i>
                系統管理
            </h2>
            <div>
                <button class="btn btn-outline-primary" onclick="refreshStats()">
                    <i class="fas fa-sync-alt me-1"></i>刷新統計
                </button>
                <button class="btn btn-warning" onclick="rebuildIndex()">
                    <i class="fas fa-hammer me-1"></i>重建索引
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 系統統計 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalDocs">-</h4>
                        <p class="mb-0">總文檔數</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalChunks">-</h4>
                        <p class="mb-0">文字片段數</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-puzzle-piece fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalSize">-</h4>
                        <p class="mb-0">總檔案大小</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="aiStatus">-</h4>
                        <p class="mb-0">AI服務狀態</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-robot fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分類統計 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    分類統計
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="categoryStats">
                    <!-- 分類統計將在這裡動態載入 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文檔列表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    文檔列表
                </h5>
                <div>
                    <select class="form-select form-select-sm" id="categoryFilter" onchange="filterDocuments()">
                        <option value="all">所有分類</option>
                        {% for key, value in categories.items() %}
                        <option value="{{ key }}">{{ value }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文檔名稱</th>
                                <th>分類</th>
                                <th>大小</th>
                                <th>處理時間</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="documentsTable">
                            <!-- 文檔列表將在這裡動態載入 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分頁 -->
                <nav aria-label="文檔分頁">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分頁將在這裡動態載入 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- AI服務狀態 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain me-2"></i>
                    AI服務狀態
                </h5>
            </div>
            <div class="card-body">
                <div id="aiServiceInfo">
                    <!-- AI服務信息將在這裡動態載入 -->
                </div>
                <button class="btn btn-outline-primary" onclick="testAI()">
                    <i class="fas fa-vial me-1"></i>測試AI連接
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 確認刪除模態框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">確認刪除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>確定要刪除文檔 <strong id="deleteFileName"></strong> 嗎？</p>
                <p class="text-danger">此操作無法復原！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">確認刪除</button>
            </div>
        </div>
    </div>
</div>

<!-- 重建索引模態框 -->
<div class="modal fade" id="rebuildModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重建索引</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>確定要重建整個文檔索引嗎？</p>
                <p class="text-warning">此操作可能需要較長時間，期間搜索功能可能受影響。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmRebuild()">確認重建</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/management.js') }}"></script>
{% endblock %}
