// 主應用程式 JavaScript

$(document).ready(function() {
    console.log('知識管理系統已載入');
    
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 平滑滾動
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });
    
    // 全局錯誤處理
    window.addEventListener('error', function(e) {
        console.error('JavaScript錯誤:', e.error);
        showAlert('系統發生錯誤，請重新整理頁面', 'danger');
    });
});

// 顯示警告訊息
function showAlert(message, type = 'info', duration = 5000) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 移除現有的警告
    $('.alert').remove();
    
    // 添加新警告到頁面頂部
    $('main.container').prepend(alertHtml);
    
    // 自動隱藏
    if (duration > 0) {
        setTimeout(() => {
            $('.alert').fadeOut();
        }, duration);
    }
}

// 獲取警告圖標
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 顯示載入模態框
function showLoadingModal(message = '正在處理您的請求，請稍候...') {
    $('#loadingModal .modal-body p').text(message);
    $('#loadingModal').modal('show');
}

// 隱藏載入模態框
function hideLoadingModal() {
    $('#loadingModal').modal('hide');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 驗證文件類型
function validateFileType(file) {
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    return allowedTypes.includes(file.type);
}

// 驗證文件大小
function validateFileSize(file, maxSize = 50 * 1024 * 1024) { // 50MB
    return file.size <= maxSize;
}

// 複製文字到剪貼簿
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showAlert('已複製到剪貼簿', 'success', 2000);
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

// 備用複製方法
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showAlert('已複製到剪貼簿', 'success', 2000);
    } catch (err) {
        showAlert('複製失敗，請手動複製', 'warning');
    }
    
    document.body.removeChild(textArea);
}

// 防抖函數
function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// 節流函數
function throttle(func, limit) {
    var inThrottle;
    return function() {
        var args = arguments;
        var context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
