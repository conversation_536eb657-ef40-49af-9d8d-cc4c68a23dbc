"""
文檔索引服務
整合文檔處理、向量檢索和文檔管理
"""

from typing import Dict, List, Optional
from loguru import logger

from app.services.document_processor import DocumentProcessor
from app.services.vector_search import VectorSearchService
from app.models.document import Document, DocumentManager


class IndexingService:
    """文檔索引服務"""
    
    def __init__(self, config):
        self.config = config
        
        # 初始化各個服務
        self.document_processor = DocumentProcessor(config)
        self.vector_search = VectorSearchService(config)
        self.document_manager = DocumentManager(config.get('UPLOAD_FOLDER', './uploads'))
        
        logger.info("文檔索引服務初始化完成")
    
    def index_document(self, file_path: str, category: str) -> Dict:
        """
        索引文檔（完整流程）
        
        Args:
            file_path: 文檔路徑
            category: 文檔分類
            
        Returns:
            索引結果
        """
        try:
            logger.info(f"開始索引文檔: {file_path}")
            
            # 1. 處理文檔，提取文字
            doc_result = self.document_processor.process_document(file_path, category)
            
            # 2. 檢查是否已存在相同文檔
            if self.document_manager.document_exists(doc_result['file_md5']):
                logger.warning(f"文檔已存在: {doc_result['file_name']}")
                return {
                    'success': False,
                    'message': '文檔已存在，跳過索引',
                    'file_md5': doc_result['file_md5']
                }
            
            # 3. 創建文檔對象
            document = Document(
                file_path=doc_result['file_path'],
                file_name=doc_result['file_name'],
                file_size=doc_result['file_size'],
                file_md5=doc_result['file_md5'],
                category=doc_result['category'],
                text_content=doc_result['text_content'],
                text_length=doc_result['text_length'],
                page_count=doc_result['page_count'],
                processed_at=doc_result['processed_at']
            )
            
            # 4. 分割文字為片段
            chunks = self.document_processor.split_text_into_chunks(
                doc_result['text_content'],
                chunk_size=self.config.get('CHUNK_SIZE', 600),
                overlap=self.config.get('CHUNK_OVERLAP', 125)
            )
            
            if not chunks:
                logger.warning(f"文檔沒有有效的文字內容: {doc_result['file_name']}")
                return {
                    'success': False,
                    'message': '文檔沒有有效的文字內容',
                    'file_md5': doc_result['file_md5']
                }
            
            # 5. 添加到向量數據庫
            vector_success = self.vector_search.add_document(document, chunks)
            if not vector_success:
                logger.error(f"添加到向量數據庫失敗: {doc_result['file_name']}")
                return {
                    'success': False,
                    'message': '添加到向量數據庫失敗',
                    'file_md5': doc_result['file_md5']
                }
            
            # 6. 添加到文檔管理器
            document.add_chunks(chunks)
            doc_success = self.document_manager.add_document(document)
            if not doc_success:
                # 如果文檔管理器添加失敗，需要從向量數據庫中移除
                self.vector_search.remove_document(document.doc_id)
                logger.error(f"添加到文檔管理器失敗: {doc_result['file_name']}")
                return {
                    'success': False,
                    'message': '添加到文檔管理器失敗',
                    'file_md5': doc_result['file_md5']
                }
            
            logger.info(f"文檔索引完成: {doc_result['file_name']}")
            return {
                'success': True,
                'message': '文檔索引成功',
                'document': document.to_dict(),
                'chunks_count': len(chunks)
            }
            
        except Exception as e:
            logger.error(f"文檔索引失敗: {e}")
            return {
                'success': False,
                'message': f'文檔索引失敗: {str(e)}',
                'error': str(e)
            }
    
    def search_documents(self, query: str, category: str = "all", 
                        top_k: int = 5) -> Dict:
        """
        搜索文檔
        
        Args:
            query: 查詢文本
            category: 文檔分類
            top_k: 返回結果數量
            
        Returns:
            搜索結果
        """
        try:
            logger.info(f"搜索文檔: query='{query}', category='{category}'")
            
            # 執行向量檢索
            search_results = self.vector_search.search(query, category, top_k)
            
            if not search_results:
                return {
                    'success': True,
                    'results': [],
                    'message': '未找到相關文檔'
                }
            
            # 整理搜索結果
            formatted_results = []
            seen_documents = set()
            
            for result in search_results:
                metadata = result['metadata']
                doc_id = metadata['doc_id']
                
                # 獲取文檔詳細信息
                document = self.document_manager.get_document(doc_id)
                if not document:
                    continue
                
                # 避免重複文檔
                if doc_id in seen_documents:
                    continue
                seen_documents.add(doc_id)
                
                formatted_result = {
                    'file_name': metadata['file_name'],
                    'file_path': metadata['file_path'],
                    'category': metadata['category'],
                    'relevance_score': result['relevance_score'],
                    'excerpt': result['text'][:200] + '...' if len(result['text']) > 200 else result['text'],
                    'chunk_id': metadata['chunk_id'],
                    'doc_id': doc_id
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"搜索完成，找到 {len(formatted_results)} 個相關文檔")
            return {
                'success': True,
                'results': formatted_results,
                'total_results': len(formatted_results),
                'query': query,
                'category': category
            }
            
        except Exception as e:
            logger.error(f"搜索文檔失敗: {e}")
            return {
                'success': False,
                'message': f'搜索失敗: {str(e)}',
                'error': str(e)
            }
    
    def remove_document(self, doc_id: str) -> Dict:
        """
        移除文檔
        
        Args:
            doc_id: 文檔ID
            
        Returns:
            移除結果
        """
        try:
            # 從向量數據庫移除
            vector_success = self.vector_search.remove_document(doc_id)
            
            # 從文檔管理器移除
            doc_success = self.document_manager.remove_document(doc_id)
            
            if vector_success and doc_success:
                logger.info(f"文檔移除成功: {doc_id}")
                return {
                    'success': True,
                    'message': '文檔移除成功'
                }
            else:
                logger.warning(f"文檔移除部分失敗: {doc_id}")
                return {
                    'success': False,
                    'message': '文檔移除部分失敗'
                }
                
        except Exception as e:
            logger.error(f"移除文檔失敗: {e}")
            return {
                'success': False,
                'message': f'移除文檔失敗: {str(e)}',
                'error': str(e)
            }
    
    def get_statistics(self) -> Dict:
        """獲取系統統計信息"""
        try:
            doc_stats = self.document_manager.get_statistics()
            vector_stats = self.vector_search.get_statistics()
            
            return {
                'documents': doc_stats,
                'vector_database': vector_stats,
                'system_status': 'healthy'
            }
            
        except Exception as e:
            logger.error(f"獲取統計信息失敗: {e}")
            return {
                'system_status': 'error',
                'error': str(e)
            }
    
    def rebuild_index(self) -> Dict:
        """重建索引"""
        try:
            logger.info("開始重建索引")
            
            # 重置向量數據庫
            self.vector_search.reset_database()
            
            # 重新索引所有文檔
            all_documents = self.document_manager.get_all_documents()
            success_count = 0
            error_count = 0
            
            for document in all_documents:
                try:
                    # 重新處理文檔
                    chunks = self.document_processor.split_text_into_chunks(
                        document.text_content,
                        chunk_size=self.config.get('CHUNK_SIZE', 600),
                        overlap=self.config.get('CHUNK_OVERLAP', 125)
                    )
                    
                    # 添加到向量數據庫
                    if self.vector_search.add_document(document, chunks):
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    logger.error(f"重建索引失敗 {document.file_name}: {e}")
                    error_count += 1
            
            logger.info(f"索引重建完成，成功: {success_count}，失敗: {error_count}")
            return {
                'success': True,
                'message': '索引重建完成',
                'success_count': success_count,
                'error_count': error_count
            }
            
        except Exception as e:
            logger.error(f"重建索引失敗: {e}")
            return {
                'success': False,
                'message': f'重建索引失敗: {str(e)}',
                'error': str(e)
            }
