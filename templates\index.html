{% extends "base.html" %}

{% block title %}首頁 - 公司知識管理系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-light p-5 rounded mb-4">
            <h1 class="display-4">
                <i class="fas fa-search me-3"></i>
                智能文檔檢索
            </h1>
            <p class="lead">快速查找公司內部文檔，獲得精準答案</p>
        </div>
    </div>
</div>

<!-- 搜索區域 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    問題查詢
                </h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="categorySelect" class="form-label">文檔分類</label>
                            <select class="form-select" id="categorySelect" name="category">
                                <option value="all">全部分類</option>
                                {% for key, value in categories.items() %}
                                <option value="{{ key }}">{{ value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-9 mb-3">
                            <label for="queryInput" class="form-label">請輸入您的問題</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="queryInput" 
                                       placeholder="例如：如何配置網路設定？" name="query">
                                <button class="btn btn-primary" type="submit" id="searchBtn">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 搜索結果區域 -->
<div class="row mb-5" id="searchResults" style="display: none;">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    AI 回答
                </h5>
            </div>
            <div class="card-body">
                <div id="answerContent">
                    <!-- AI回答內容將在這裡顯示 -->
                </div>
                
                <hr>
                
                <h6 class="text-muted">
                    <i class="fas fa-file-alt me-2"></i>
                    參考來源
                </h6>
                <div id="sourcesContent">
                    <!-- 來源文檔將在這裡顯示 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文檔上傳區域 -->
<div class="row mb-5" id="upload-section">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    文檔上傳
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="uploadCategory" class="form-label">選擇分類</label>
                            <select class="form-select" id="uploadCategory" name="category" required>
                                <option value="">請選擇分類</option>
                                {% for key, value in categories.items() %}
                                <option value="{{ key }}">{{ value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-8 mb-3">
                            <label for="fileInput" class="form-label">選擇文檔</label>
                            <input type="file" class="form-control" id="fileInput" name="file" 
                                   accept=".pdf,.docx" required>
                            <div class="form-text">
                                支援格式：PDF、DOCX，最大檔案大小：50MB
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-info" id="uploadBtn">
                                <i class="fas fa-upload me-1"></i>上傳文檔
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- 上傳進度 -->
                <div id="uploadProgress" style="display: none;" class="mt-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- 上傳結果 -->
                <div id="uploadResult" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- 載入中模態框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-3 mb-0">正在處理您的請求，請稍候...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/search.js') }}"></script>
<script src="{{ url_for('static', filename='js/upload.js') }}"></script>
{% endblock %}
