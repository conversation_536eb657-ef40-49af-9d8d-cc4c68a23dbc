"""
文檔數據模型
用於管理文檔信息和元數據
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from loguru import logger


class Document:
    """文檔模型"""
    
    def __init__(self, file_path: str, file_name: str, file_size: int,
                 file_md5: str, category: str, text_content: str = "",
                 text_length: int = 0, page_count: int = 0,
                 processed_at: str = None, doc_id: str = None):
        self.doc_id = doc_id or file_md5  # 使用MD5作為文檔ID
        self.file_path = file_path
        self.file_name = file_name
        self.file_size = file_size
        self.file_md5 = file_md5
        self.category = category
        self.text_content = text_content
        self.text_length = text_length
        self.page_count = page_count
        self.processed_at = processed_at or datetime.now().isoformat()
        self.chunks = []  # 文字片段列表
    
    def to_dict(self) -> Dict:
        """轉換為字典格式"""
        return {
            'doc_id': self.doc_id,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'file_md5': self.file_md5,
            'category': self.category,
            'text_content': self.text_content,
            'text_length': self.text_length,
            'page_count': self.page_count,
            'processed_at': self.processed_at,
            'chunks_count': len(self.chunks)
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Document':
        """從字典創建文檔對象"""
        return cls(
            file_path=data['file_path'],
            file_name=data['file_name'],
            file_size=data['file_size'],
            file_md5=data['file_md5'],
            category=data['category'],
            text_content=data.get('text_content', ''),
            text_length=data.get('text_length', 0),
            page_count=data.get('page_count', 0),
            processed_at=data.get('processed_at'),
            doc_id=data.get('doc_id')
        )
    
    def add_chunks(self, chunks: List[Dict]):
        """添加文字片段"""
        self.chunks = chunks
        logger.info(f"文檔 {self.file_name} 添加了 {len(chunks)} 個片段")


class DocumentChunk:
    """文檔片段模型"""
    
    def __init__(self, doc_id: str, chunk_id: int, text: str,
                 start_pos: int, end_pos: int, embedding: List[float] = None):
        self.doc_id = doc_id
        self.chunk_id = chunk_id
        self.text = text
        self.start_pos = start_pos
        self.end_pos = end_pos
        self.embedding = embedding or []
        self.chunk_hash = self._calculate_chunk_hash()
    
    def _calculate_chunk_hash(self) -> str:
        """計算片段哈希值"""
        import hashlib
        content = f"{self.doc_id}_{self.chunk_id}_{self.text}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def to_dict(self) -> Dict:
        """轉換為字典格式"""
        return {
            'doc_id': self.doc_id,
            'chunk_id': self.chunk_id,
            'text': self.text,
            'start_pos': self.start_pos,
            'end_pos': self.end_pos,
            'chunk_hash': self.chunk_hash,
            'embedding_size': len(self.embedding)
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DocumentChunk':
        """從字典創建片段對象"""
        return cls(
            doc_id=data['doc_id'],
            chunk_id=data['chunk_id'],
            text=data['text'],
            start_pos=data['start_pos'],
            end_pos=data['end_pos'],
            embedding=data.get('embedding', [])
        )


class DocumentManager:
    """文檔管理器"""
    
    def __init__(self, storage_path: str):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.metadata_file = self.storage_path / 'documents_metadata.json'
        self._documents = {}
        self._load_metadata()
    
    def _load_metadata(self):
        """載入文檔元數據"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for doc_data in data.get('documents', []):
                        doc = Document.from_dict(doc_data)
                        self._documents[doc.doc_id] = doc
                logger.info(f"載入了 {len(self._documents)} 個文檔的元數據")
            except Exception as e:
                logger.error(f"載入元數據失敗: {e}")
                self._documents = {}
    
    def _save_metadata(self):
        """保存文檔元數據"""
        try:
            data = {
                'documents': [doc.to_dict() for doc in self._documents.values()],
                'last_updated': datetime.now().isoformat()
            }
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"保存了 {len(self._documents)} 個文檔的元數據")
        except Exception as e:
            logger.error(f"保存元數據失敗: {e}")
    
    def add_document(self, document: Document) -> bool:
        """添加文檔"""
        try:
            if self.document_exists(document.file_md5):
                logger.warning(f"文檔已存在: {document.file_name} (MD5: {document.file_md5})")
                return False
            
            self._documents[document.doc_id] = document
            self._save_metadata()
            logger.info(f"添加文檔: {document.file_name}")
            return True
        except Exception as e:
            logger.error(f"添加文檔失敗: {e}")
            return False
    
    def get_document(self, doc_id: str) -> Optional[Document]:
        """獲取文檔"""
        return self._documents.get(doc_id)
    
    def document_exists(self, file_md5: str) -> bool:
        """檢查文檔是否已存在"""
        return file_md5 in self._documents
    
    def get_documents_by_category(self, category: str) -> List[Document]:
        """根據分類獲取文檔"""
        return [doc for doc in self._documents.values() if doc.category == category]
    
    def get_all_documents(self) -> List[Document]:
        """獲取所有文檔"""
        return list(self._documents.values())
    
    def remove_document(self, doc_id: str) -> bool:
        """移除文檔"""
        try:
            if doc_id in self._documents:
                doc = self._documents[doc_id]
                
                # 刪除文件
                if os.path.exists(doc.file_path):
                    os.remove(doc.file_path)
                
                # 從內存中移除
                del self._documents[doc_id]
                self._save_metadata()
                
                logger.info(f"移除文檔: {doc.file_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除文檔失敗: {e}")
            return False
    
    def update_document(self, document: Document) -> bool:
        """更新文檔"""
        try:
            self._documents[document.doc_id] = document
            self._save_metadata()
            logger.info(f"更新文檔: {document.file_name}")
            return True
        except Exception as e:
            logger.error(f"更新文檔失敗: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """獲取統計信息"""
        stats = {
            'total_documents': len(self._documents),
            'categories': {},
            'total_size': 0,
            'total_text_length': 0
        }
        
        for doc in self._documents.values():
            # 分類統計
            if doc.category not in stats['categories']:
                stats['categories'][doc.category] = 0
            stats['categories'][doc.category] += 1
            
            # 大小統計
            stats['total_size'] += doc.file_size
            stats['total_text_length'] += doc.text_length
        
        return stats
